"use server";

import { prisma } from "@/lib/database";
import { revalidatePath } from "next/cache";
import { handleActionError, createErrorResult, type ActionResult } from "@/lib/error-utils";

export type DeleteActionState = {
  success: boolean;
  message: string;
  error: string;
};

export async function deleteProduct(
  id: string
): Promise<DeleteActionState> {
  try {
    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            variants: true,
            images: true,
            reviews: true,
          },
        },
      },
    });

    if (!product) {
      return {
        success: false,
        message: "",
        error: "Product not found",
      };
    }

    // Safety check - prevent deletion if product has variants or reviews
    if (product._count.variants > 0) {
      return {
        success: false,
        message: "",
        error: "Cannot delete product with existing variants. Please delete variants first.",
      };
    }

    if (product._count.reviews > 0) {
      return {
        success: false,
        message: "",
        error: "Cannot delete product with existing reviews. Consider deactivating instead.",
      };
    }

    // Delete the product (this will cascade delete images due to foreign key constraints)
    await prisma.product.delete({
      where: { id },
    });

    // Revalidate the products page to reflect changes
    revalidatePath('/admin/products');

    return {
      success: true,
      message: "Product deleted successfully",
      error: "",
    };

  } catch (error) {
    console.error('Error deleting product:', error);
    
    // Handle specific database errors
    if (error instanceof Error) {
      if (error.message.includes('Foreign key constraint')) {
        return {
          success: false,
          message: "",
          error: "Cannot delete product due to existing references. Please remove related data first.",
        };
      }
    }

    return {
      success: false,
      message: "",
      error: "An unexpected error occurred while deleting the product",
    };
  }
}

export async function bulkDeleteProducts(
  ids: string[]
): Promise<DeleteActionState> {
  try {
    if (!ids || ids.length === 0) {
      return {
        success: false,
        message: "",
        error: "No products selected for deletion",
      };
    }

    // Check for products with variants or reviews
    const productsWithConstraints = await prisma.product.findMany({
      where: {
        id: { in: ids },
      },
      include: {
        _count: {
          select: {
            variants: true,
            reviews: true,
          },
        },
      },
    });

    const problematicProducts = productsWithConstraints.filter(
      p => p._count.variants > 0 || p._count.reviews > 0
    );

    if (problematicProducts.length > 0) {
      const names = problematicProducts.map(p => p.name).join(', ');
      return {
        success: false,
        message: "",
        error: `Cannot delete products with variants or reviews: ${names}`,
      };
    }

    // Delete all selected products
    const result = await prisma.product.deleteMany({
      where: {
        id: { in: ids },
      },
    });

    // Revalidate the products page
    revalidatePath('/admin/products');

    return {
      success: true,
      message: `Successfully deleted ${result.count} product(s)`,
      error: "",
    };

  } catch (error) {
    console.error('Error bulk deleting products:', error);
    
    return {
      success: false,
      message: "",
      error: "An unexpected error occurred while deleting products",
    };
  }
}

export async function toggleProductStatus(
  id: string,
  isActive: boolean
): Promise<DeleteActionState> {
  try {
    await prisma.product.update({
      where: { id },
      data: { isActive },
    });

    // Revalidate the products page
    revalidatePath('/admin/products');

    return {
      success: true,
      message: `Product ${isActive ? 'activated' : 'deactivated'} successfully`,
      error: "",
    };

  } catch (error) {
    console.error('Error toggling product status:', error);
    
    return {
      success: false,
      message: "",
      error: "An unexpected error occurred while updating product status",
    };
  }
}
