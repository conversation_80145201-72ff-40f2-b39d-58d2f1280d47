import { getProductForEdit } from "@/actions/products/get-product";
import { prisma } from "@/lib/database";
import { notFound } from "next/navigation";
import EditProductForm from "./form";

interface EditProductPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditProductPage({ params }: EditProductPageProps) {
  const { id } = await params;
  
  try {
    // Fetch product and categories in parallel
    const [product, categories] = await Promise.all([
      getProductForEdit(id),
      prisma.category.findMany({
        select: {
          id: true,
          name: true,
          slug: true,
        },
        orderBy: {
          name: 'asc',
        },
      })
    ]);

    if (!product) {
      notFound();
    }

    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Edit Product</h1>
          <p className="text-gray-600 mt-2">
            Update product information for "{product.name}"
          </p>
        </div>
        
        <EditProductForm product={product} categories={categories} />
      </div>
    );
  } catch (error) {
    console.error('Error loading product for edit:', error);
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error Loading Product
          </h2>
          <p className="text-gray-600 mb-4">
            There was an error loading the product for editing. Please try again.
          </p>
          <button 
            onClick={() => window.location.reload()} 
            className="text-blue-600 hover:text-blue-800"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }
}
