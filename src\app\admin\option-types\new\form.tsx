"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { createOptionType, type ActionState } from "@/actions/option-types/create-option-types";
import { useActionState, useState } from "react";
import { useRouter } from "next/navigation";
import { X, Plus } from "lucide-react";

export default function CreateOptionTypeForm() {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(createOptionType, {
    message: "",
    error: "",
    fieldErrors: {},
  } as ActionState);

  const [optionValues, setOptionValues] = useState<string[]>([]);
  const [valueInput, setValueInput] = useState("");

  const handleAddValue = () => {
    const trimmed = valueInput.trim();
    if (trimmed && !optionValues.includes(trimmed)) {
      setOptionValues([...optionValues, trimmed]);
      setValueInput("");
    }
  };

  const handleRemoveValue = (valueToRemove: string) => {
    setOptionValues(optionValues.filter(value => value !== valueToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddValue();
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <form action={formAction} className="space-y-6">
        {/* Hidden inputs for option values */}
        {optionValues.map((value, index) => (
          <Input
            key={index}
            type="hidden"
            name={`optionValue_${index}`}
            value={value}
          />
        ))}

        {/* Feedback messages */}
        {state.message && (
          <div className="rounded-md border border-green-300 bg-green-50 p-4">
            <p className="text-green-800">{state.message}</p>
          </div>
        )}

        {state.error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-4">
            <p className="text-red-800">{state.error}</p>
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Create Option Type</CardTitle>
            <CardDescription>Provide a name, description, and option values.</CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Option Name */}
            <div className="space-y-1.5">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                name="name"
                required
                placeholder="e.g., Color, Size, Material"
                className={state.fieldErrors?.name ? "border-red-500" : ""}
              />
              {state.fieldErrors?.name && (
                <p className="text-sm text-red-600">{state.fieldErrors.name}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-1.5">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                rows={3}
                placeholder="Optional description for this option type..."
                className={state.fieldErrors?.description ? "border-red-500" : ""}
              />
              {state.fieldErrors?.description && (
                <p className="text-sm text-red-600">{state.fieldErrors.description}</p>
              )}
            </div>

            {/* Option Values */}
            <div className="space-y-2">
              <Label htmlFor="valueInput">Option Values *</Label>
              <div className="flex gap-2">
                <Input
                  id="valueInput"
                  value={valueInput}
                  onChange={(e) => setValueInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="e.g., Red, Blue, Large, Small"
                />
                <Button type="button" onClick={handleAddValue} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {optionValues.length > 0 ? (
                <div className="flex flex-wrap gap-2 mt-2">
                  {optionValues.map((value, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {value}
                    <Button
  type="button"
  variant={"ghost"}
  onClick={() => handleRemoveValue(value)}
  className="ml-1 text-muted-foreground hover:text-destructive"
>
  <X className="h-3 w-3" />
</Button>

                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 mt-1">Add at least one value</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/admin/option-types")}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isPending || optionValues.length === 0}>
            {isPending ? "Creating..." : "Create Option Type"}
          </Button>
        </div>
      </form>
    </div>
  );
}
