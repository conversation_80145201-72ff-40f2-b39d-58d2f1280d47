"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { updateCategory, type ActionState } from "@/actions/categories/update-category";
import { useActionState, useState } from "react";
import { useRouter } from "next/navigation";

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string | null;
}

interface EditCategoryFormProps {
  category: Category;
}

export default function EditCategoryForm({ category }: EditCategoryFormProps) {
  const router = useRouter();
  const updateCategoryWithId = updateCategory.bind(null, category.id);
  const [state, formAction, isPending] = useActionState(updateCategoryWithId, {
    message: "",
    error: "",
    fieldErrors: {},
  } as ActionState);

  const [name, setName] = useState(category.name);
  const [slug, setSlug] = useState(category.slug);

  const generateSlug = (text: string) => {
    return text
      .toLowerCase()
      .replace(/[åä]/g, 'a')
      .replace(/ö/g, 'o')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
    // Only auto-generate slug if it matches the current generated slug
    if (slug === generateSlug(category.name)) {
      setSlug(generateSlug(newName));
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <form action={formAction} className="space-y-6">
        {/* Error and Success Messages */}
        {state.message && (
          <div className="rounded-md border border-green-300 bg-green-50 p-4">
            <p className="text-green-800">{state.message}</p>
          </div>
        )}
        
        {state.error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-4">
            <p className="text-red-800">{state.error}</p>
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Category Information</CardTitle>
            <CardDescription>Update category details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Category Name *</Label>
              <Input 
                id="name" 
                name="name" 
                value={name}
                onChange={handleNameChange}
                required 
                className={state.fieldErrors?.name ? "border-red-500" : ""}
              />
              {state.fieldErrors?.name && (
                <p className="text-sm text-red-600">{state.fieldErrors.name}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="slug">URL Slug *</Label>
              <Input 
                id="slug" 
                name="slug" 
                value={slug}
                onChange={(e) => setSlug(e.target.value)}
                required 
                className={state.fieldErrors?.slug ? "border-red-500" : ""}
              />
              {state.fieldErrors?.slug && (
                <p className="text-sm text-red-600">{state.fieldErrors.slug}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea 
                id="description" 
                name="description" 
                rows={3}
                defaultValue={category.description || ""}
                placeholder="Optional description for this category..."
                className={state.fieldErrors?.description ? "border-red-500" : ""}
              />
              {state.fieldErrors?.description && (
                <p className="text-sm text-red-600">{state.fieldErrors.description}</p>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button 
            type="button" 
            variant="outline"
            onClick={() => router.push('/admin/categories')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isPending}>
            {isPending ? "Updating..." : "Update Category"}
          </Button>
        </div>
      </form>
    </div>
  );
}
