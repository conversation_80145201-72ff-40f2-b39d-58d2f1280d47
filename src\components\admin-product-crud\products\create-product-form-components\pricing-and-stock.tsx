import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";

interface PricingAndStockStepProps {
  fieldErrors?: Record<string, string>;
  isActive: boolean;
  setIsActive: (value: boolean) => void;
  formData?: {
    basePrice: string;
    sku: string;
    stock: string;
  };
  onFormDataChange?: (field: string, value: string) => void;
}

export default function PricingAndStockStep({
  fieldErrors,
  isActive,
  setIsActive,
  formData,
  onFormDataChange
}: PricingAndStockStepProps) {
  const [basePrice, setBasePrice] = useState(formData?.basePrice || "");
  const [sku, setSku] = useState(formData?.sku || "");
  const [stock, setStock] = useState(formData?.stock || "");

  const handleBasePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPrice = e.target.value;
    setBasePrice(newPrice);
    onFormDataChange?.('basePrice', newPrice);
  };

  const handleSkuChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSku = e.target.value;
    setSku(newSku);
    onFormDataChange?.('sku', newSku);
  };

  const handleStockChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newStock = e.target.value;
    setStock(newStock);
    onFormDataChange?.('stock', newStock);
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Pricing & Inventory</CardTitle>
        <CardDescription>Price, stock, and product codes</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="basePrice">Base Price (SEK) *</Label>
            <Input
              id="basePrice"
              name="basePrice"
              type="number"
              step="0.01"
              min="0"
              value={basePrice}
              onChange={handleBasePriceChange}
              required
              className={fieldErrors?.basePrice ? "border-red-500" : ""}
            />
            {fieldErrors?.basePrice && (
              <p className="text-sm text-red-600">{fieldErrors.basePrice}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="sku">SKU *</Label>
            <Input
              id="sku"
              name="sku"
              value={sku}
              onChange={handleSkuChange}
              required
              placeholder="e.g., PROD-001"
              className={fieldErrors?.sku ? "border-red-500" : ""}
            />
            {fieldErrors?.sku && (
              <p className="text-sm text-red-600">{fieldErrors.sku}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="stock">Stock Quantity *</Label>
            <Input
              id="stock"
              name="stock"
              type="number"
              min="0"
              value={stock}
              onChange={handleStockChange}
              required
              className={fieldErrors?.stock ? "border-red-500" : ""}
            />
            {fieldErrors?.stock && (
              <p className="text-sm text-red-600">{fieldErrors.stock}</p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="isActive"
            checked={isActive}
            onCheckedChange={(checked) => setIsActive(checked === true)}
          />
          <Label htmlFor="isActive">Product is active (visible to customers)</Label>
        </div>
      </CardContent>
    </Card>
  );
}