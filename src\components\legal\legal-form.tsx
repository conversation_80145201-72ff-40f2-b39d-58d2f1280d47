"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { marked } from "marked";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { createLegalPage } from "@/lib/actions/legal";
import { LegalPageCategory, LegalPageStatus } from "@/generated/prisma";

interface LegalFormProps {
  onSuccess?: () => void;
}

export function LegalForm({ onSuccess }: LegalFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string[]>>({});
  const [showPreview, setShowPreview] = useState(false);
  const [currentContent, setCurrentContent] = useState("");
  const router = useRouter();

  async function handleSubmit(formData: FormData) {
    setIsSubmitting(true);
    setErrors({});

    const data = {
      title: formData.get("title") as string,
      slug: formData.get("slug") as string,
      content: currentContent, // Use the state value instead of form data
      category: formData.get("category") as LegalPageCategory,
      version: formData.get("version") as string || "1.0",
      status: formData.get("status") as LegalPageStatus || "DRAFT",
    };

    const result = await createLegalPage(data);

    if (result.success) {
      toast.success(result.error || "Legal page created successfully!");
      if (onSuccess) {
        onSuccess();
      } else {
        router.refresh();
      }
      // Reset form
      const form = document.getElementById("legal-form") as HTMLFormElement;
      form?.reset();
    } else {
      toast.error(result.error || "Failed to create legal page");
      if (result.fieldErrors) {
        setErrors(result.fieldErrors);
      }
    }

    setIsSubmitting(false);
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New Legal Page</CardTitle>
      </CardHeader>
      <CardContent>
        <form id="legal-form" action={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              name="title"
              placeholder="Enter page title"
              required
              className={errors.title ? "border-red-500" : ""}
            />
            {errors.title && (
              <p className="text-sm text-red-500">{errors.title[0]}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                name="slug"
                placeholder="e.g. terms-of-service"
                required
                className={errors.slug ? "border-red-500" : ""}
              />
              {errors.slug && (
                <p className="text-sm text-red-500">{errors.slug[0]}</p>
              )}
              <p className="text-sm text-muted-foreground">
                This will be the URL path (e.g., /terms-of-service)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="version">Version</Label>
              <Input
                id="version"
                name="version"
                placeholder="1.0"
                defaultValue="1.0"
                required
                className={errors.version ? "border-red-500" : ""}
              />
              {errors.version && (
                <p className="text-sm text-red-500">{errors.version[0]}</p>
              )}
              <p className="text-sm text-muted-foreground">
                Version number (e.g., 1.0, 2.1)
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select name="category" required>
                <SelectTrigger className={errors.category ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="TERMS_OF_SERVICE">Köpvillkor</SelectItem>
                  <SelectItem value="PRIVACY_POLICY">Integritetspolicy</SelectItem>
                  <SelectItem value="DELIVERY_TERMS">Leveransvillkor</SelectItem>
                  <SelectItem value="RETURN_POLICY">Returpolicy</SelectItem>
                  <SelectItem value="ABOUT_US">Om oss</SelectItem>
                  <SelectItem value="RIGHT_OF_WITHDRAWAL">Ångerrätt</SelectItem>
                  <SelectItem value="COOKIE_POLICY">Cookie Policy</SelectItem>
                  <SelectItem value="OTHER">Other</SelectItem>
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-red-500">{errors.category[0]}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Initial Status</Label>
              <Select name="status" defaultValue="DRAFT" required>
                <SelectTrigger className={errors.status ? "border-red-500" : ""}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DRAFT">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <span>Draft - Work in progress</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="REVIEW">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>Review - Coming soon</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="PUBLISHED">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Published - Live immediately</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-red-500">{errors.status[0]}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="content">Content</Label>
              <div className="flex items-center space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPreview(!showPreview)}
                >
                  {showPreview ? "Edit" : "Preview"}
                </Button>
              </div>
            </div>

            {showPreview ? (
              <div className="border rounded-lg p-4 min-h-[250px] bg-gray-50">
                {currentContent ? (
                  <div className="prose dark:prose-invert max-w-none">
                    <div dangerouslySetInnerHTML={{ __html: marked(currentContent) }} />
                  </div>
                ) : (
                  <p className="text-gray-500 italic">Start typing content to see preview...</p>
                )}
              </div>
            ) : (
              <Textarea
                id="content"
                name="content"
                value={currentContent}
                onChange={(e) => setCurrentContent(e.target.value)}
                placeholder="Enter the legal page content using Markdown formatting..."
                rows={10}
                required
                className={errors.content ? "border-red-500" : ""}
              />
            )}

            {errors.content && (
              <p className="text-sm text-red-500">{errors.content[0]}</p>
            )}
            <p className="text-sm text-muted-foreground">
              You can use Markdown formatting (# for headings, ** for bold, etc.)
            </p>
          </div>

          <Button type="submit" disabled={isSubmitting} className="w-full">
            {isSubmitting ? "Creating..." : "Create Legal Page"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
