"use server";

import { prisma } from "@/lib/database";
import { z } from "zod";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";


// type of state returned by the form action (used for validation feedback)

export type ActionState = {
  message: string;
  error: string;
  fieldErrors: Record<string, string>;
};

//zod validation of categories
const categorySchema = z.object({
  name: z.string().min(1, "Name is required"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
  description: z.string().optional(),
});


export async function createCategory(prevState: ActionState, formData: FormData): Promise<ActionState> {
  const rawData = Object.fromEntries(formData.entries());

 const result = categorySchema.safeParse(rawData);
 
 // if validation fails return the errors
 if (!result.success) {
   const fieldErrors: Record<string, string> = {};
   Object.entries(result.error.flatten().fieldErrors).forEach(([field, errors]) => {
     if (errors && errors.length > 0) {
       fieldErrors[field] = errors[0];
     }
   });

    return {
      message: "",
      error: "Validation failed.Please fix the errors below and try again.",
      fieldErrors,
    };
  }


 // If validation passed, extract the data
  const data = result.data;

try {
  //first check if category exist by slug(unique)
  const existingCategory = await prisma.category.findUnique({
  where: { slug: data.slug },
});

if (existingCategory) {
  return {
    message: "",
    error: "A category with this slug already exists.",
    fieldErrors: {
      slug: "This slug is already taken.",
    },
  };
}

  // Placeholder:
  // await prisma.category.create({ data });
  await prisma.category.create({
  data: {
    name: data.name,
    slug: data.slug,
    description: data.description,
  },
});
    revalidatePath("/admin/categories");
  } catch (error) {
    console.error("Database error:", error);
    return {
      message: "",
      error: "An error occurred while creating the category.",
      fieldErrors: {},
    };
  } 

  redirect("/admin/categories");
}