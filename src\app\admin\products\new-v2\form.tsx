"use client";

import { useState, useActionState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { createProductV2 } from "./actions";
import { Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { Info } from "lucide-react";

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface CreateProductFormV2Props {
  categories: Category[];
}

export default function CreateProductFormV2({ categories }: CreateProductFormV2Props) {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(createProductV2, {
    message: "",
    error: "",
    fieldErrors: {},
  });

  // Local state for dynamic fields
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");

  // Tag management
  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleTagKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  // Category management
  const toggleCategory = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <form action={formAction} className="space-y-4">
        {/* Hidden fields for categories and tags(dynamic) */}
        <input type="hidden" name="categoryIds" value={JSON.stringify(selectedCategories)} />
        <input type="hidden" name="tags" value={JSON.stringify(tags)} />

        {/* Error Display */}
        {state.error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-3">
            <p className="text-red-800 text-sm">{state.error}</p>
            {state.fieldErrors && Object.keys(state.fieldErrors).length > 0 && (
              <ul className="mt-1 list-disc list-inside text-xs text-red-700">
                {Object.entries(state.fieldErrors).map(([field, error]) => (
                  <li key={field}>{field}: {error}</li>
                ))}
              </ul>
            )}
          </div>
        )}

        {/*INfo at the top ( must be filled) */}
        <div className="p-4 space-y-3">
          <h3 className="font-semibold text-lg">Essential Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <Label htmlFor="name" className="text-sm font-medium flex items-center gap-1">
  Product Name
  <span className="text-red-500 font-bold" title="Required">*</span>
</Label>

              <Label htmlFor="name" className="text-sm font-medium">Product Name 
                <span className="bg-red-100 text-red-600 text-xs px-1.5 py-0.5 rounded">required</span>

              </Label>
              <Input
                id="name"
                name="name"
                placeholder="Enter product name"
                className={`mt-1 ${state.fieldErrors?.name ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.name && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="slug" className="text-sm font-medium">URL Slug *</Label>
              <Input
                id="slug"
                name="slug"
                placeholder="product-url-slug"
                className={`mt-1 ${state.fieldErrors?.slug ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.slug && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.slug}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="description" className="text-sm font-medium">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Product description"
              rows={2}
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <Label htmlFor="basePrice" className="text-sm font-medium">Price (SEK) *</Label>
              <Input
                id="basePrice"
                name="basePrice"
                type="number"
                step="0.01"
                placeholder="0.00"
                className={`mt-1 ${state.fieldErrors?.basePrice ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.basePrice && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.basePrice}</p>
              )}
            </div>

            <div>
              <Label htmlFor="sku" className="text-sm font-medium">SKU *</Label>
              <Input
                id="sku"
                name="sku"
                placeholder="PRODUCT-SKU-001"
                className={`mt-1 ${state.fieldErrors?.sku ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.sku && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.sku}</p>
              )}
            </div>

            <div>
              <Label htmlFor="stock" className="text-sm font-medium">Stock *</Label>
              <Input
                id="stock"
                name="stock"
                type="number"
                placeholder="0"
                className={`mt-1 ${state.fieldErrors?.stock ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.stock && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.stock}</p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Checkbox id="isActive" name="isActive" defaultChecked />
              <Label htmlFor="isActive" className="text-sm">Active</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="hasOption" name="hasOption" />
  <Label htmlFor="hasOption" className="text-sm flex items-center gap-1">
    Has variants
    <Tooltip>
      <TooltipTrigger asChild>
        <Info className="h-4 w-4 text-muted-foreground cursor-pointer" />
      </TooltipTrigger>
      <TooltipContent side="right">
        Check this if your product comes in multiple variants (e.g., size, color).
      </TooltipContent>
    </Tooltip>
  </Label>            </div>
          </div>
        </div>

        {/* Categories - required at least one, fetched from prisma */}
        <div className="p-4 space-y-3">
          <h3 className="font-semibold">Categories *</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 max-h-32 overflow-y-auto">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category.id}`}
                  checked={selectedCategories.includes(category.id)}
                  onCheckedChange={() => toggleCategory(category.id)}
                />
                <Label htmlFor={`category-${category.id}`} className="text-sm">
                  {category.name}
                </Label>
              </div>
            ))}
          </div>
          {state.fieldErrors?.categoryIds && (
            <p className="text-xs text-red-600">{state.fieldErrors.categoryIds}</p>
          )}
        </div>

        {/* optionals ?  - collapsed by default */}
        <details>
          <summary className="p-4 cursor-pointer font-semibold bg-gray-50 border rounded-lg">Optional Fields</summary>
          <div className="p-4 pt-0 space-y-4">

            {/* physical properties */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Physical Properties</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <div>
                  <Label htmlFor="weight" className="text-xs">Weight (kg)</Label>
                  <Input
                    id="weight"
                    name="weight"
                    type="number"
                    step="0.001"
                    placeholder="0.000"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="length" className="text-xs">Length (cm)</Label>
                  <Input
                    id="length"
                    name="length"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="width" className="text-xs">Width (cm)</Label>
                  <Input
                    id="width"
                    name="width"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="heigth" className="text-xs">Height (cm)</Label>
                  <Input
                    id="heigth"
                    name="heigth"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="mt-1"
                  />
                </div>
              </div>
            </div>

            {/* SEO (?) */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">SEO Fields</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="metaTitle" className="text-xs">Meta Title</Label>
                  <Input
                    id="metaTitle"
                    name="metaTitle"
                    placeholder="SEO title"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="metaDescription" className="text-xs">Meta Description</Label>
                  <Input
                    id="metaDescription"
                    name="metaDescription"
                    placeholder="SEO description"
                    className="mt-1"
                  />
                </div>
              </div>
            </div>

            {/* tags (?) */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Tags</h4>
              <div className="flex space-x-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={handleTagKeyDown}
                  placeholder="Add a tag"
                  className="flex-1"
                />
                <Button type="button" onClick={addTag} variant="outline" size="sm">
                  Add
                </Button>
              </div>

              <div className="flex flex-wrap gap-1">
                {tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs flex items-center gap-1">
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </details>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/products')}
          >
            Cancel
          </Button>

          <Button type="submit" disabled={isPending}>
            {isPending ? "Creating..." : "Create Product"}
          </Button>
        </div>
      </form>
    </div>
  );
}
