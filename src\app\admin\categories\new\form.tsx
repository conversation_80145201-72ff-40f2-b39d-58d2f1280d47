"use client";
import { useActionState, useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { createCategory, type ActionState } from "../../../../actions/categories/create-category";
import { useRouter } from "next/navigation";


export default function CreateCategoryForm() {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(createCategory, {
    message: "",
    error: "",
    fieldErrors: {},
  } as ActionState);

    //storing the category name
  const [name, setName] = useState("");

  //storing the generated or manually entered slug
  const [slug, setSlug] = useState("");

  //generate a slug func
  const generateSlug = (text: string) => {
    return text
      .toLowerCase() 
      .replace(/[åä]/g, 'a') // replace Swedish characters å, ä with 'a'
      .replace(/ö/g, 'o') // replace Swedish character ö with 'o'
      .replace(/[^a-z0-9\s-]/g, '') // remove all non-alphanumeric chars
      .replace(/\s+/g, '-') // replace spaces with hyphens
      .replace(/-+/g, '-') // replace multiple hyphens with a single one
      .trim(); 
  };

 useEffect(() => {
   if (name && !slug) {
     setSlug(generateSlug(name));
   }
 }, [name, slug]);


  return (
    <div className="max-w-2xl mx-auto">
      <form action={formAction} className="space-y-6">
        {/* Error and Success Messages */}
        {state.message && (
          <div className="rounded-md border border-green-300 bg-green-50 p-4">
            <p className="text-green-800">{state.message}</p>
          </div>
        )}
        
        {state.error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-4">
            <p className="text-red-800">{state.error}</p>
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Category Information</CardTitle>
            <CardDescription>Basic details for the new category</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Category Name *</Label>
              <Input 
                id="name" 
                name="name" 
                value={name}
                onChange={(e) => setName(e.target.value)}
                required 
                className={state.fieldErrors?.name ? "border-red-500" : ""}
              />
              {state.fieldErrors?.name && (
                <p className="text-sm text-red-600">{state.fieldErrors.name}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="slug">URL Slug *</Label>
              <Input 
                id="slug" 
                name="slug" 
                value={slug}
                onChange={(e) => setSlug(e.target.value)}
                required 
                className={state.fieldErrors?.slug ? "border-red-500" : ""}
              />
              {state.fieldErrors?.slug && (
                <p className="text-sm text-red-600">{state.fieldErrors.slug}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea 
                id="description" 
                name="description" 
                rows={3}
                placeholder="Optional description for this category..."
                className={state.fieldErrors?.description ? "border-red-500" : ""}
              />
              {state.fieldErrors?.description && (
                <p className="text-sm text-red-600">{state.fieldErrors.description}</p>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button 
            type="button" 
            variant="outline"
            onClick={() => router.push('/admin/categories')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isPending}>
            {isPending ? "Creating..." : "Create Category"}
          </Button>
        </div>
      </form>
    </div>
  );
}
