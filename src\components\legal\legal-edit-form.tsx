"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { marked } from "marked";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { VersionHelper } from "@/components/legal/version-helper";
import { updateLegalPage } from "@/lib/actions/legal";
import { LegalPage, LegalPageCategory, LegalPageStatus } from "@/generated/prisma";

interface LegalEditFormProps {
  legalPage: LegalPage;
}

export function LegalEditForm({ legalPage }: LegalEditFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string[]>>({});
  const [currentVersion, setCurrentVersion] = useState(legalPage.version);
  const [showPreview, setShowPreview] = useState(false);
  const [currentContent, setCurrentContent] = useState(legalPage.content);
  const router = useRouter();

  async function handleUpdate(formData: FormData) {
    setIsSubmitting(true);
    setErrors({});

    const data = {
      title: formData.get("title") as string,
      slug: formData.get("slug") as string,
      content: currentContent, // Use the state value instead of form data
      category: formData.get("category") as LegalPageCategory,
      version: formData.get("version") as string,
      status: formData.get("status") as LegalPageStatus,
      metaTitle: formData.get("metaTitle") as string || undefined,
      metaDescription: formData.get("metaDescription") as string || undefined,
    };

    const autoIncrementVersion = formData.get("autoIncrementVersion") === "on";

    const result = await updateLegalPage(legalPage.id, data, autoIncrementVersion);

    if (result.success) {
      toast.success(result.error || "Legal page updated successfully!");
      router.refresh();
    } else {
      toast.error(result.error || "Failed to update legal page");
      if (result.fieldErrors) {
        setErrors(result.fieldErrors);
      }
    }

    setIsSubmitting(false);
  }



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-100 text-green-800';
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800';
      case 'REVIEW':
        return 'bg-blue-100 text-blue-800';
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Edit Form */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Edit Legal Page</CardTitle>
            <Badge className={getStatusColor(legalPage.status)}>
              {legalPage.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <form action={handleUpdate} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  name="title"
                  defaultValue={legalPage.title}
                  required
                  className={errors.title ? "border-red-500" : ""}
                />
                {errors.title && (
                  <p className="text-sm text-red-500">{errors.title[0]}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  name="slug"
                  defaultValue={legalPage.slug}
                  required
                  className={errors.slug ? "border-red-500" : ""}
                />
                {errors.slug && (
                  <p className="text-sm text-red-500">{errors.slug[0]}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="version">Version</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="version"
                    name="version"
                    value={currentVersion}
                    onChange={(e) => setCurrentVersion(e.target.value)}
                    required
                    className={errors.version ? "border-red-500" : ""}
                  />
                  <VersionHelper
                    currentVersion={currentVersion}
                    onVersionChange={setCurrentVersion}
                  />
                </div>
                {errors.version && (
                  <p className="text-sm text-red-500">{errors.version[0]}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Format: X.Y (e.g., 1.0, 2.1)
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select name="category" defaultValue={legalPage.category} required>
                  <SelectTrigger className={errors.category ? "border-red-500" : ""}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="TERMS_OF_SERVICE">Köpvillkor</SelectItem>
                    <SelectItem value="PRIVACY_POLICY">Integritetspolicy</SelectItem>
                    <SelectItem value="DELIVERY_TERMS">Leveransvillkor</SelectItem>
                    <SelectItem value="RETURN_POLICY">Returpolicy</SelectItem>
                    <SelectItem value="ABOUT_US">Om oss</SelectItem>
                    <SelectItem value="RIGHT_OF_WITHDRAWAL">Ångerrätt</SelectItem>
                    <SelectItem value="COOKIE_POLICY">Cookie Policy</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className="text-sm text-red-500">{errors.category[0]}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select name="status" defaultValue={legalPage.status} required>
                  <SelectTrigger className={errors.status ? "border-red-500" : ""}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DRAFT">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span>Draft - Work in progress</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="REVIEW">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Review - Coming soon</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="PUBLISHED">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Published - Live</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="ARCHIVED">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                        <span>Archived - Hidden</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-500">{errors.status[0]}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Set the publication status of this page
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="content">Content</Label>
                <div className="flex items-center space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowPreview(!showPreview)}
                  >
                    {showPreview ? "Edit" : "Preview"}
                  </Button>
                </div>
              </div>

              {showPreview ? (
                <div className="border rounded-lg p-4 min-h-[400px] bg-gray-50">
                  <div className="prose dark:prose-invert max-w-none">
                    <div dangerouslySetInnerHTML={{ __html: marked(currentContent) }} />
                  </div>
                </div>
              ) : (
                <>
                  <Textarea
                    id="content"
                    name="content"
                    value={currentContent}
                    onChange={(e) => setCurrentContent(e.target.value)}
                    rows={15}
                    required
                    className={errors.content ? "border-red-500" : ""}
                  />
                  {/* Hidden input to ensure form submission gets the current content */}
                  <input type="hidden" name="actualContent" value={currentContent} />
                </>
              )}

              {errors.content && (
                <p className="text-sm text-red-500">{errors.content[0]}</p>
              )}
              <p className="text-sm text-muted-foreground">
                You can use Markdown formatting (# for headings, ** for bold, etc.)
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="metaTitle">Meta Title (SEO)</Label>
                <Input
                  id="metaTitle"
                  name="metaTitle"
                  defaultValue={legalPage.metaTitle || ""}
                  placeholder="SEO title for search engines"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="metaDescription">Meta Description (SEO)</Label>
                <Input
                  id="metaDescription"
                  name="metaDescription"
                  defaultValue={legalPage.metaDescription || ""}
                  placeholder="SEO description for search engines"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="autoIncrementVersion"
                  name="autoIncrementVersion"
                />
                <Label htmlFor="autoIncrementVersion" className="text-sm">
                  Auto-increment version if content changed
                </Label>
              </div>
            </div>

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Status Management */}
      <Card>
        <CardHeader>
          <CardTitle>Page Information</CardTitle>
          <div className="text-sm text-muted-foreground">
            Current status: <strong>{legalPage.status}</strong>. Use the status field above to change the publication status.
          </div>
        </CardHeader>
        <CardContent>

          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
            <div>
              <p><strong>Created:</strong> {new Date(legalPage.createdAt).toLocaleString()}</p>
              <p><strong>Last Updated:</strong> {new Date(legalPage.updatedAt).toLocaleString()}</p>
              <p><strong>Version:</strong> {legalPage.version}</p>
            </div>
            <div>
              {legalPage.lastReviewedAt && (
                <p><strong>Last Reviewed:</strong> {new Date(legalPage.lastReviewedAt).toLocaleString()}</p>
              )}
              {legalPage.effectiveDate && (
                <p><strong>Effective Date:</strong> {new Date(legalPage.effectiveDate).toLocaleString()}</p>
              )}
              {legalPage.expiryDate && (
                <p><strong>Expiry Date:</strong> {new Date(legalPage.expiryDate).toLocaleString()}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
