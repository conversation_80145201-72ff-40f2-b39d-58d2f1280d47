"use client";

import { useState } from "react";
import { toast } from "sonner";
import { marked } from "marked";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { restoreLegalPageVersion } from "@/lib/actions/legal";

interface HistoryEntry {
  id: string;
  version: string;
  status: string;
  changeReason?: string | null;
  changedBy?: string | null;
  createdAt: Date;
  title: string;
  content: string;
}

interface LegalPageHistoryProps {
  pageId: string;
  currentVersion: string;
  history: HistoryEntry[];
  onRestore?: () => void;
}

export function LegalPageHistory({ pageId, currentVersion, history, onRestore }: LegalPageHistoryProps) {
  const [selectedHistory, setSelectedHistory] = useState<HistoryEntry | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  async function handleRestore(historyId: string, version: string) {
    const result = await restoreLegalPageVersion(pageId, historyId);
    
    if (result.success) {
      toast.success(`Restored to version ${version} successfully!`);
      if (onRestore) {
        onRestore();
      }
    } else {
      toast.error(result.error || "Failed to restore version");
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-100 text-green-800';
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800';
      case 'REVIEW':
        return 'bg-blue-100 text-blue-800';
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Version History</CardTitle>
        <p className="text-sm text-muted-foreground">
          View and restore previous versions of this legal page
        </p>
      </CardHeader>
      <CardContent>
        {history.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="mb-4">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p>No version history available yet.</p>
            <p className="text-xs mt-1">History will be created when you make changes to this page.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {history.map((entry) => (
              <div key={entry.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline" className="font-mono">
                      v{entry.version}
                    </Badge>
                    <Badge className={getStatusColor(entry.status)}>
                      {entry.status}
                    </Badge>
                    {entry.version === currentVersion && (
                      <Badge variant="default">Current</Badge>
                    )}
                  </div>
                  <div className="mt-2 text-sm text-gray-600">
                    <p className="font-medium">{entry.changeReason || "No reason provided"}</p>
                    <p className="text-xs">
                      {new Date(entry.createdAt).toLocaleString()}
                      {entry.changedBy && ` • by ${entry.changedBy}`}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Dialog open={isDialogOpen && selectedHistory?.id === entry.id} onOpenChange={setIsDialogOpen}>
                    <DialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setSelectedHistory(entry)}
                      >
                        View
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Version {entry.version} - {entry.title}</DialogTitle>
                        <DialogDescription>
                          Created on {new Date(entry.createdAt).toLocaleString()}
                          {entry.changedBy && ` by ${entry.changedBy}`}
                        </DialogDescription>
                      </DialogHeader>
                      
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="font-mono">v{entry.version}</Badge>
                          <Badge className={getStatusColor(entry.status)}>{entry.status}</Badge>
                        </div>
                        
                        {entry.changeReason && (
                          <div>
                            <h4 className="font-medium text-sm">Change Reason:</h4>
                            <p className="text-sm text-gray-600">{entry.changeReason}</p>
                          </div>
                        )}
                        
                        <div>
                          <h4 className="font-medium text-sm mb-2">Content:</h4>
                          <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                            <div className="prose prose-sm" dangerouslySetInnerHTML={{ __html: marked(entry.content) }} />
                          </div>
                        </div>
                      </div>

                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                          Close
                        </Button>
                        {entry.version !== currentVersion && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="default">
                                Restore This Version
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Restore Version {entry.version}?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  This will restore the page to version {entry.version}. The current version will be saved to history. This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleRestore(entry.id, entry.version)}>
                                  Restore Version
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                  
                  {entry.version !== currentVersion && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          Restore
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Restore Version {entry.version}?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will restore the page to version {entry.version}. The current version will be saved to history.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleRestore(entry.id, entry.version)}>
                            Restore
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
