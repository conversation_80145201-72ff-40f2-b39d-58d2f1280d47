"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import { LegalPage } from "@/generated/prisma";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { deleteLegalPage } from "@/lib/actions/legal";
import { Edit, Trash2, Eye, Globe } from "lucide-react";

interface LegalPagesTableProps {
  legalPages: LegalPage[];
}

export function LegalPagesTable({ legalPages }: LegalPagesTableProps) {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const router = useRouter();

  async function handleDelete(id: string, _title: string) {
    const result = await deleteLegalPage(id);

    if (result.success) {
      toast.success(result.error || "Legal page deleted successfully!");
      router.refresh();
    } else {
      toast.error(result.error || "Failed to delete legal page");
    }
  }

  const filteredPages = legalPages.filter(page => {
    if (statusFilter === "all") return true;
    return page.status === statusFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'REVIEW':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return 'Live and publicly visible';
      case 'DRAFT':
        return 'Work in progress';
      case 'REVIEW':
        return 'Awaiting approval';
      case 'ARCHIVED':
        return 'No longer active';
      default:
        return '';
    }
  };

  const formatCategory = (category: string) => {
    return category.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const getStatusCounts = () => {
    const counts = {
      all: legalPages.length,
      DRAFT: legalPages.filter(p => p.status === 'DRAFT').length,
      REVIEW: legalPages.filter(p => p.status === 'REVIEW').length,
      PUBLISHED: legalPages.filter(p => p.status === 'PUBLISHED').length,
      ARCHIVED: legalPages.filter(p => p.status === 'ARCHIVED').length,
    };
    return counts;
  };

  const statusCounts = getStatusCounts();

  if (legalPages.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Legal Pages</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">
            No legal pages found. Create your first legal page using the form below.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Legal Pages ({filteredPages.length} of {legalPages.length})</CardTitle>
          <div className="flex items-center space-x-4">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All ({statusCounts.all})</SelectItem>
                <SelectItem value="DRAFT">Draft ({statusCounts.DRAFT})</SelectItem>
                <SelectItem value="REVIEW">Review ({statusCounts.REVIEW})</SelectItem>
                <SelectItem value="PUBLISHED">Published ({statusCounts.PUBLISHED})</SelectItem>
                <SelectItem value="ARCHIVED">Archived ({statusCounts.ARCHIVED})</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        {statusCounts.REVIEW > 0 && (
          <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
            📋 {statusCounts.REVIEW} page(s) awaiting review
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4 font-medium">Title</th>
                <th className="text-left py-3 px-4 font-medium">Slug</th>
                <th className="text-left py-3 px-4 font-medium">Category</th>
                <th className="text-left py-3 px-4 font-medium">Version</th>
                <th className="text-left py-3 px-4 font-medium">Status</th>
                <th className="text-left py-3 px-4 font-medium">Updated</th>
                <th className="text-left py-3 px-4 font-medium">View As</th>
                <th className="text-left py-3 px-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredPages.map((page) => (
                <tr key={page.id} className="border-b hover:bg-muted/50">
                  <td className="py-3 px-4">
                    <div className="font-medium">{page.title}</div>
                    {page.metaTitle && (
                      <div className="text-sm text-muted-foreground">
                        SEO: {page.metaTitle}
                      </div>
                    )}
                  </td>
                  <td className="py-3 px-4">
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      /{page.slug}
                    </code>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-sm">
                      {formatCategory(page.category)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <Badge variant="outline" className="font-mono">
                      v{page.version}
                    </Badge>
                  </td>
                  <td className="py-3 px-4">
                    <div className="space-y-1">
                      <Badge variant="secondary" className={getStatusColor(page.status)}>
                        {page.status}
                      </Badge>
                      <div className="text-xs text-muted-foreground">
                        {getStatusDescription(page.status)}
                      </div>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="text-sm">
                      {new Date(page.updatedAt).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(page.updatedAt).toLocaleTimeString()}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex space-x-1">
                      <Button asChild variant="outline" size="sm" className="h-8 w-8 p-0">
                        <Link href={`/admin/legal/preview/${page.slug}`} title="Admin Preview">
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      {page.status === 'PUBLISHED' && (
                        <Button asChild variant="outline" size="sm" className="h-8 w-8 p-0">
                          <Link href={`/${page.slug}`} target="_blank" title="Public View">
                            <Globe className="h-4 w-4" />
                          </Link>
                        </Button>
                      )}
                      {page.status !== 'PUBLISHED' && (
                        <div className="h-8 w-8 flex items-center justify-center text-gray-400" title="Not published">
                          <Globe className="h-4 w-4" />
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex space-x-1">
                      <Button asChild variant="outline" size="sm" className="h-8 w-8 p-0">
                        <Link href={`/admin/legal/${page.id}`} title="Edit">
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" size="sm" className="h-8 w-8 p-0" title="Delete">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Legal Page</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to permanently delete &quot;{page.title}&quot;? This action cannot be undone and will remove all version history.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDelete(page.id, page.title)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete Permanently
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
