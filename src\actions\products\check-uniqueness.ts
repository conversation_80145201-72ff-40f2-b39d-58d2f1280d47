"use server";

import { prisma } from "@/lib/database";

export async function checkSlugUniqueness(slug: string): Promise<{ available: boolean; message: string }> {
  try {
    if (!slug) {
      return { available: true, message: "Slug is available" };
    }

    const existingProduct = await prisma.product.findUnique({
      where: { slug },
      select: { id: true }
    });

    return {
      available: !existingProduct,
      message: existingProduct ? "This slug is already taken" : "Slug is available"
    };
  } catch (error) {
    console.error("Error checking slug:", error);
    return {
      available: true,
      message: "Could not verify slug availability"
    };
  }
}

export async function checkSkuUniqueness(sku: string): Promise<{ available: boolean; message: string }> {
  try {
    if (!sku) {
      return { available: true, message: "SKU is available" };
    }

    const existingProduct = await prisma.product.findUnique({
      where: { sku },
      select: { id: true }
    });

    return {
      available: !existingProduct,
      message: existingProduct ? "This SKU is already taken" : "SKU is available"
    };
  } catch (error) {
    console.error("Error checking SKU:", error);
    return {
      available: true,
      message: "Could not verify SKU availability"
    };
  }
}
