import CreateProductForm from "./form";
import { prisma } from "@/lib/database";


export default async function CreateProductPage() {
 //getting categories for the form checkboxes:
   const categories = await prisma.category.findMany({
    select: {
      id: true,
      name: true,
      slug: true,
    },
    orderBy: {
      name: 'asc',
    },
  });

  return (
    <div>
        <div>
           <h1 className="font-semibold text-2xl text-center"> Create Product </h1>
           </div>
        <CreateProductForm categories={categories} />
    </div>
  )
}