### For required fields: 
              <Label htmlFor="name" className="text-sm font-medium flex items-center gap-1">
  Product Name
  <span className="text-red-500 font-bold" title="Required">*</span>
</Label>

or 

              <Label htmlFor="name" className="text-sm font-medium">Product Name 
                <span className="bg-red-100 text-red-600 text-xs px-1.5 py-0.5 rounded">required</span>

              </Label>


### For small instructions: 
npx shadcn@latest add tooltip

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

<Tooltip>
  <TooltipTrigger>Hover</TooltipTrigger>
  <TooltipContent>
    <p>Add to library</p>
  </TooltipContent>
</Tooltip>




##What is done : 
1. Validation rules: 
[] name, slug, sku, basePrice, stock, etc. are required.

[] Optional fields like description, weight, etc. are optional.

[] Numbers to have at most 2 decimal places (for prices).

[] Slug has only lowercase letters, numbers, and hyphens.

2. Types: 
type ActionState = { ... }
type ProcessedFormData = { ... }
 
 --ActionState is what the frontend expects as feedback from the server.
 --ProcessedFormData is what the server expects from the frontend.

 3. Convert Error Format: 
 function convertToLegacyState(result: ActionResult): ActionState
 explanation: Sometimes errors come back as arrays like ["This is required", "Must be valid"].
 This function just takes the first message and sends it back cleanly to the form.



4. Cook the raw data : 
const processedData = {
  ...rawData,
  categoryIds: JSON.parse(...),
  tags: JSON.parse(...),
  isActive: formData.has("isActive"),
  hasOption: formData.has("hasOption"),
};


---> 
[]  Convert categoryIds and tags (sent as JSON strings) into arrays.

[]  Turn checkboxes (isActive, hasOption) into true or false.


5. Collect all errors: 
if (!result.success) {
  ...collect field-level errors...
}

---> even if a few fields fail, we collect all errors and show them at once. 

6. Slug and SKU 
await prisma.product.findUnique({ where: { slug: ... } });
If another product already uses the same slug or sku, add errors for those too.

7.  If any rrrors → return early
if (Object.keys(allErrors).length > 0) {
  return convertToLegacyState(...);
}
[] If any error was found (either from validation or database checks), stop here and return errors to the form.

8. If everything is good → create a product

--->
Send the final clean data to your database.
And connect it to categories.

9. If something sgoes wrong → catch block

} catch (error) {
  return convertToLegacyState(handleActionError(error));
}
If any unknown error happens, catch it and return a safe error message.

