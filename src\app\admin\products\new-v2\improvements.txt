### For required fields: 
              <Label htmlFor="name" className="text-sm font-medium flex items-center gap-1">
  Product Name
  <span className="text-red-500 font-bold" title="Required">*</span>
</Label>

or 

              <Label htmlFor="name" className="text-sm font-medium">Product Name 
                <span className="bg-red-100 text-red-600 text-xs px-1.5 py-0.5 rounded">required</span>

              </Label>


### For small instructions: 
npx shadcn@latest add tooltip

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

<Tooltip>
  <TooltipTrigger>Hover</TooltipTrigger>
  <TooltipContent>
    <p>Add to library</p>
  </TooltipContent>
</Tooltip>
