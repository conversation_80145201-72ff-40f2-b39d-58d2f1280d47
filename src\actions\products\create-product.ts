"use server";

import { prisma } from "@/lib/database";
import { redirect } from "next/navigation";
import { fullProductSchema } from "@/validations/productStepSchemas";
import z from "zod";

export type ActionState = {
  message: string;
  error: string;
  fieldErrors: Record<string, string>;
  warnings?: string[];
}

function getProductWarnings(data: z.infer<typeof fullProductSchema>) {
  const warnings: string[] = []

  if (data.slug.length > 60) {
    warnings.push("Slug is too long and might be truncated in URLs.")
  }

  if (!data.metaDescription || data.metaDescription.length < 50) {
    warnings.push("Meta description is short or missing — this may hurt SEO.")
  }

  if (!data.weight && !data.length && !data.width && !data.heigth) {
    warnings.push("No dimensions provided — this may be needed for shipping.")
  }

  if (!data.tags || data.tags.length === 0) {
    warnings.push("Tags help with internal search. Consider adding some.")
  }

  return warnings
}




export async function createProduct(prevState: unknown, formData: FormData) {
    console.log("Hello from the server!");

    const rawData = Object.fromEntries(formData.entries());
    
      // Parse JSON fields
  const processedData = {
      ...rawData,
      categoryIds: rawData.categoryIds ? JSON.parse(rawData.categoryIds as string) : [],
      tags: rawData.tags ? JSON.parse(rawData.tags as string) : [],
      isActive: rawData.isActive === 'true',
      hasOption: rawData.hasOption === 'true',
  };

  //debuggging
      console.log("Processed data:", processedData);

    const result = fullProductSchema.safeParse(processedData);
   if (!result.success) {
      console.log("Validation errors:", result.error.flatten());
      const fieldErrors = result.error.flatten().fieldErrors;

      // Convert field errors to user-friendly messages
      const friendlyErrors: Record<string, string> = {};
      Object.entries(fieldErrors).forEach(([field, errors]) => {
          if (errors && errors.length > 0) {
              friendlyErrors[field] = errors[0]; // Take first error message
          }
      });

      return {
          message: "",
          error: "Please fix the errors below and try again.",
          fieldErrors: friendlyErrors,
      };
  }

    const data = result.data;
    const warnings = getProductWarnings(data)

    try{
        const product = await prisma.product.create({
        data: {
            name: data.name,
            slug: data.slug,
            description: data.description,
            basePrice: data.basePrice,
            sku: data.sku,
            stock: data.stock,
            isActive: data.isActive,
            hasOption: data.hasOption,
            weight: data.weight,
            length: data.length,
            width: data.width,
            heigth: data.heigth,
            metaTitle: data.metaTitle,
            metaDescription: data.metaDescription,
            tags: data.tags,
            categories: {
                connect: data.categoryIds.map((id: string) => ({ id })),
            },
        },
    });
        console.log("Product created:", product);
          // Return success with warnings if any
    if (warnings.length > 0) {
        return {
            message: "Product created successfully!",
            error: "",
            warnings,
            fieldErrors: {},
        };
    }
      } catch (error) {
            console.error("Database error:", error);

            // Handle specific database errors
            if (error instanceof Error) {
                if (error.message.includes('Unique constraint failed on the fields: (`slug`)')) {
                    return {
                        message: "",
                        error: "A product with this URL slug already exists. Please choose a different slug.",
                        fieldErrors: { slug: "This slug is already taken" },
                    };
                }

                if (error.message.includes('Unique constraint failed on the fields: (`sku`)')) {
                    return {
                        message: "",
                        error: "A product with this SKU already exists. Please choose a different SKU.",
                        fieldErrors: { sku: "This SKU is already taken" },
                    };
                }
            }

        return {
            message: "",
            error: "An error occurred while creating the product. Please try again.",
            fieldErrors: {},
        };
    }

    redirect("/admin/products");
}