import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ProductStatusIndicator } from "./ProductStatusIndicator";
import { Edit, Eye, Trash2, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { ProductListItem } from "@/actions/products/get-products";

interface ProductCardProps {
  product: ProductListItem;
  onDelete?: (id: string) => void;
  onToggleStatus?: (id: string, isActive: boolean) => void;
}

export function ProductCard({ product, onDelete, onToggleStatus }: ProductCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK',
    }).format(price);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('sv-SE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(date));
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg truncate">{product.name}</h3>
            <p className="text-sm text-gray-600 mt-1">SKU: {product.sku}</p>
            <p className="text-sm text-gray-500">/{product.slug}</p>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/admin/products/${product.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/admin/products/${product.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onToggleStatus?.(product.id, !product.isActive)}
              >
                {product.isActive ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onDelete?.(product.id)}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Price and Status */}
        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold text-green-600">
            {formatPrice(product.basePrice)}
          </div>
          <ProductStatusIndicator
            isActive={product.isActive}
            stock={product.stock}
            hasVariants={product.hasOption}
            size="sm"
          />
        </div>

        {/* Categories */}
        <div className="space-y-2">
          <p className="text-sm font-medium text-gray-700">Categories:</p>
          <div className="flex flex-wrap gap-1">
            {product.categories.length > 0 ? (
              product.categories.map((category) => (
                <Badge key={category.id} variant="outline" className="text-xs">
                  {category.name}
                </Badge>
              ))
            ) : (
              <span className="text-xs text-gray-500">No categories</span>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <span className="font-medium">Stock:</span> {product.stock}
          </div>
          <div>
            <span className="font-medium">Variants:</span> {product._count.variants}
          </div>
        </div>

        {/* Created Date */}
        <div className="text-xs text-gray-500 pt-2 border-t">
          Created: {formatDate(product.createdAt)}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button asChild size="sm" variant="outline" className="flex-1">
            <Link href={`/admin/products/${product.id}`}>
              <Eye className="h-3 w-3 mr-1" />
              View
            </Link>
          </Button>
          <Button asChild size="sm" className="flex-1">
            <Link href={`/admin/products/${product.id}/edit`}>
              <Edit className="h-3 w-3 mr-1" />
              Edit
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
