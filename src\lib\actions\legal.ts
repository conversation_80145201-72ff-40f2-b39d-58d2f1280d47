'use server'

import { prisma } from "@/lib/database";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { LegalPageCategory, LegalPageStatus } from "@/generated/prisma";
import { ActionResult, createSuccessResult, handleActionError } from "@/lib/error-utils";
import { swedishLegalTemplates } from "@/lib/swedish-templates";

// Type definitions for better type safety
type LegalPageData = {
  title: string;
  slug: string;
  content: string;
  category: LegalPageCategory;
  version: string;
  status: LegalPageStatus;
  effectiveDate?: Date;
  expiryDate?: Date;
  metaTitle?: string;
  metaDescription?: string;
};

type TemplateCustomData = {
  title?: string;
  slug: string;
  status?: string;
  metaTitle?: string;
  metaDescription?: string;
};

type Template = {
  id: string;
  name: string;
  description: string | null;
  category: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  content: string;
};

// Validation schemas
const legalPageSchema = z.object({
  title: z.string().min(1, "Title is required"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
  content: z.string().min(1, "Content is required"),
  category: z.nativeEnum(LegalPageCategory),
  version: z.string().min(1, "Version is required"),
  status: z.nativeEnum(LegalPageStatus),
  effectiveDate: z.date().optional(),
  expiryDate: z.date().optional(),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
});

// Helper function to save page history
async function savePageHistory(page: {
  id: string;
  title: string;
  slug: string;
  content: string;
  version: string;
  status: LegalPageStatus;
  effectiveDate: Date | null;
  expiryDate: Date | null;
  category: LegalPageCategory;
  metaTitle: string | null;
  metaDescription: string | null;
}, changeReason: string) {
  await prisma.legalPageHistory.create({
    data: {
      legalPageId: page.id,
      title: page.title,
      slug: page.slug,
      content: page.content,
      version: page.version,
      status: page.status,
      effectiveDate: page.effectiveDate,
      expiryDate: page.expiryDate,
      category: page.category,
      metaTitle: page.metaTitle,
      metaDescription: page.metaDescription,
      changeReason,
    },
  });
}

// Main CRUD operations
export async function createLegalPage(data: LegalPageData): Promise<ActionResult> {
  try {
    const validatedData = legalPageSchema.parse(data);
    
    // Check if slug already exists
    const existingPage = await prisma.legalPage.findUnique({
      where: { slug: validatedData.slug },
    });
    
    if (existingPage) {
      return handleActionError(new Error("A page with this slug already exists"));
    }

    const legalPage = await prisma.legalPage.create({
      data: validatedData,
    });

    // Save initial version to history
    await savePageHistory(legalPage, "Initial creation");

    revalidatePath('/admin/legal');
    revalidatePath(`/${legalPage.slug}`);

    return createSuccessResult(legalPage, "Legal page created successfully!");
  } catch (error) {
    return handleActionError(error);
  }
}

export async function updateLegalPage(id: string, data: Partial<LegalPageData>, autoIncrementVersion: boolean = false): Promise<ActionResult> {
  try {
    // Get current page for history
    const currentPage = await prisma.legalPage.findUnique({ where: { id } });
    if (!currentPage) {
      return handleActionError(new Error("Legal page not found"));
    }

    // Check if content has changed and auto-increment is enabled
    const finalData = { ...data };
    if (autoIncrementVersion && data.content && data.content !== currentPage.content) {
      // Auto-increment version
      const currentVersionParts = currentPage.version.split('.');
      const majorVersion = parseInt(currentVersionParts[0]) || 1;
      const minorVersion = parseInt(currentVersionParts[1]) || 0;

      // Increment minor version
      finalData.version = `${majorVersion}.${minorVersion + 1}`;
    }

    // Save current version to history before updating
    await savePageHistory(currentPage, "Update - previous version");

    const updatedPage = await prisma.legalPage.update({
      where: { id },
      data: finalData,
    });

    revalidatePath('/admin/legal');
    revalidatePath(`/${updatedPage.slug}`);
    revalidatePath(`/admin/legal/preview/${updatedPage.slug}`);

    return createSuccessResult(updatedPage, "Legal page updated successfully!");
  } catch (error) {
    return handleActionError(error);
  }
}

export async function deleteLegalPage(id: string): Promise<ActionResult> {
  try {
    const legalPage = await prisma.legalPage.findUnique({ where: { id } });
    if (!legalPage) {
      return handleActionError(new Error("Legal page not found"));
    }

    await prisma.legalPage.delete({ where: { id } });

    revalidatePath('/admin/legal');
    revalidatePath(`/${legalPage.slug}`);
    revalidatePath(`/admin/legal/preview/${legalPage.slug}`);

    return createSuccessResult(null, "Legal page deleted successfully!");
  } catch (error) {
    return handleActionError(error);
  }
}

// Read operations
export async function getLegalPages() {
  return prisma.legalPage.findMany({
    orderBy: { updatedAt: 'desc' },
  });
}

export async function getLegalPage(slug: string) {
  return prisma.legalPage.findUnique({
    where: { slug },
  });
}

export async function getLegalPageBySlug(slug: string) {
  return prisma.legalPage.findUnique({
    where: { slug },
  });
}

export async function getLegalPageById(id: string) {
  return prisma.legalPage.findUnique({
    where: { id },
  });
}

// Template operations
export async function getLegalPageTemplates(): Promise<Template[]> {
  try {
    const dbTemplates = await prisma.legalPageTemplate.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' },
    });

    if (dbTemplates.length > 0) {
      return dbTemplates;
    }
  } catch (error) {
    console.error('Error fetching templates from database:', error);
  }

  // Fallback to clean Swedish templates
  return swedishLegalTemplates;
}

export async function getLegalPageTemplate(id: string) {
  const templates = await getLegalPageTemplates();
  return templates.find(t => t.id === id);
}

export async function createLegalPageFromTemplate(templateId: string, customData: TemplateCustomData): Promise<ActionResult> {
  try {
    const template = await getLegalPageTemplate(templateId);
    if (!template) {
      return handleActionError(new Error("Template not found"));
    }

    const data: LegalPageData = {
      title: customData.title || template.name.replace(" Template", ""),
      slug: customData.slug,
      content: template.content,
      category: template.category as LegalPageCategory,
      version: "1.0",
      status: (customData.status || "DRAFT") as LegalPageStatus,
      metaTitle: customData.metaTitle,
      metaDescription: customData.metaDescription,
    };

    return await createLegalPage(data);
  } catch (error) {
    return handleActionError(error);
  }
}

// History operations
export async function getLegalPageHistory(pageId: string) {
  return prisma.legalPageHistory.findMany({
    where: { legalPageId: pageId },
    orderBy: { createdAt: 'desc' },
  });
}

export async function restoreLegalPageVersion(pageId: string, historyId: string): Promise<ActionResult> {
  try {
    const historyRecord = await prisma.legalPageHistory.findUnique({
      where: { id: historyId },
    });

    if (!historyRecord) {
      return handleActionError(new Error("History record not found"));
    }

    const currentPage = await prisma.legalPage.findUnique({
      where: { id: pageId },
    });

    if (!currentPage) {
      return handleActionError(new Error("Legal page not found"));
    }

    // Save current version to history before restoring
    await savePageHistory(currentPage, `Backup before restoring to v${historyRecord.version}`);

    // Restore the page to the historical version
    const restoredPage = await prisma.legalPage.update({
      where: { id: pageId },
      data: {
        title: historyRecord.title,
        slug: historyRecord.slug,
        content: historyRecord.content,
        version: historyRecord.version,
        status: historyRecord.status,
        effectiveDate: historyRecord.effectiveDate,
        expiryDate: historyRecord.expiryDate,
        category: historyRecord.category,
        metaTitle: historyRecord.metaTitle,
        metaDescription: historyRecord.metaDescription,
      },
    });

    revalidatePath('/admin/legal');
    revalidatePath(`/${restoredPage.slug}`);
    revalidatePath(`/admin/legal/preview/${restoredPage.slug}`);

    return createSuccessResult(restoredPage, `Successfully restored to version ${historyRecord.version}!`);
  } catch (error) {
    return handleActionError(error);
  }
}
