import { getLegalPages, getLegalPageTemplates } from "@/lib/actions/legal";
import { LegalForm } from "@/components/legal/legal-form";
import { LegalPagesTable } from "@/components/legal/legal-pages-table";
import { LegalTemplateSelector } from "@/components/legal/legal-template-selector";

export default async function AdminLegalPage() {
  const legalPages = await getLegalPages();
  const templates = await getLegalPageTemplates();

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Legal Pages Management</h1>
      </div>

      <LegalPagesTable legalPages={legalPages} />

      <LegalTemplateSelector templates={templates} />

      <LegalForm />
    </div>
  );
}
