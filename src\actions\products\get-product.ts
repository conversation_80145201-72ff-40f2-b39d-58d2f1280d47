"use server";

import { prisma } from "@/lib/database";

export interface ProductDetail {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  basePrice: number;
  sku: string;
  stock: number;
  isActive: boolean;
  hasOption: boolean;
  weight: number | null;
  length: number | null;
  width: number | null;
  heigth: number | null;
  metaTitle: string | null;
  metaDescription: string | null;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  categories: {
    id: string;
    name: string;
    slug: string;
  }[];
  images: {
    id: string;
    url: string;
    altText: string | null;
    sortOrder: number;
    isMain: boolean;
  }[];
  variants: {
    id: string;
    name: string;
    basePrice: number;
    sku: string;
    stock: number;
    isActive: boolean;
  }[];
  reviews: {
    id: string;
    rating: number;
    title: string | null;
    comment: string | null;
    isApproved: boolean;
    createdAt: Date;
    customer: {
      name: string;
    };
  }[];
  _count: {
    variants: number;
    images: number;
    reviews: number;
  };
}

export async function getProduct(id: string): Promise<ProductDetail | null> {
  try {
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        images: {
          select: {
            id: true,
            url: true,
            altText: true,
            sortOrder: true,
            isMain: true,
          },
          orderBy: {
            sortOrder: 'asc',
          },
        },
        variants: {
          select: {
            id: true,
            name: true,
            basePrice: true,
            sku: true,
            stock: true,
            isActive: true,
          },
          orderBy: {
            name: 'asc',
          },
        },
        reviews: {
          where: {
            isApproved: true,
          },
          select: {
            id: true,
            rating: true,
            title: true,
            comment: true,
            isApproved: true,
            createdAt: true,
            customer: {
              select: {
                name: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        _count: {
          select: {
            variants: true,
            images: true,
            reviews: true,
          },
        },
      },
    });

    return product as ProductDetail | null;
  } catch (error) {
    console.error('Error fetching product:', error);
    throw new Error('Failed to fetch product');
  }
}

export async function getProductBySlug(slug: string): Promise<ProductDetail | null> {
  try {
    const product = await prisma.product.findUnique({
      where: { slug },
      include: {
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        images: {
          select: {
            id: true,
            url: true,
            altText: true,
            sortOrder: true,
            isMain: true,
          },
          orderBy: {
            sortOrder: 'asc',
          },
        },
        variants: {
          select: {
            id: true,
            name: true,
            basePrice: true,
            sku: true,
            stock: true,
            isActive: true,
          },
          orderBy: {
            name: 'asc',
          },
        },
        reviews: {
          where: {
            isApproved: true,
          },
          select: {
            id: true,
            rating: true,
            title: true,
            comment: true,
            isApproved: true,
            createdAt: true,
            customer: {
              select: {
                name: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        _count: {
          select: {
            variants: true,
            images: true,
            reviews: true,
          },
        },
      },
    });

    return product as ProductDetail | null;
  } catch (error) {
    console.error('Error fetching product by slug:', error);
    throw new Error('Failed to fetch product');
  }
}

export async function getProductForEdit(id: string) {
  try {
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    return product;
  } catch (error) {
    console.error('Error fetching product for edit:', error);
    throw new Error('Failed to fetch product for editing');
  }
}
