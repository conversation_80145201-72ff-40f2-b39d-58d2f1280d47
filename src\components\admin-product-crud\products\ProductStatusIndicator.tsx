import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Package, AlertTriangle } from "lucide-react";

interface ProductStatusIndicatorProps {
  isActive: boolean;
  stock: number;
  hasVariants?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function ProductStatusIndicator({ 
  isActive, 
  stock, 
  hasVariants = false,
  size = 'md' 
}: ProductStatusIndicatorProps) {
  const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4';
  
  // Determine status based on active state and stock
  const getStatus = () => {
    if (!isActive) {
      return {
        label: 'Inactive',
        variant: 'secondary' as const,
        icon: <XCircle className={iconSize} />,
        color: 'text-gray-500'
      };
    }
    
    if (stock === 0) {
      return {
        label: 'Out of Stock',
        variant: 'destructive' as const,
        icon: <AlertTriangle className={iconSize} />,
        color: 'text-red-500'
      };
    }
    
    if (stock <= 5) {
      return {
        label: 'Low Stock',
        variant: 'outline' as const,
        icon: <AlertTriangle className={iconSize} />,
        color: 'text-orange-500'
      };
    }
    
    return {
      label: 'Active',
      variant: 'default' as const,
      icon: <CheckCircle className={iconSize} />,
      color: 'text-green-500'
    };
  };

  const status = getStatus();

  return (
    <div className="flex items-center gap-2">
      <Badge variant={status.variant} className="flex items-center gap-1">
        <span className={status.color}>
          {status.icon}
        </span>
        {status.label}
      </Badge>
      
      {hasVariants && (
        <Badge variant="outline" className="flex items-center gap-1">
          <Package className={iconSize} />
          Has Variants
        </Badge>
      )}
      
      {isActive && (
        <span className="text-sm text-gray-600">
          Stock: {stock}
        </span>
      )}
    </div>
  );
}

export function SimpleStatusBadge({ isActive }: { isActive: boolean }) {
  return (
    <Badge variant={isActive ? "default" : "secondary"}>
      {isActive ? "Active" : "Inactive"}
    </Badge>
  );
}
