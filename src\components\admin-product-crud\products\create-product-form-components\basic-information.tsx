import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useState, useEffect } from "react";

interface BasicInformationStepProps {
  fieldErrors?: Record<string, string>;
  formData?: {
    name: string;
    slug: string;
    description: string;
  };
  onFormDataChange?: (field: string, value: string) => void;
}

export default function BasicInformationStep({
  fieldErrors,
  formData,
  onFormDataChange
}: BasicInformationStepProps) {
  const [name, setName] = useState(formData?.name || "");
  const [slug, setSlug] = useState(formData?.slug || "");
  const [description, setDescription] = useState(formData?.description || "");

  // Generate slug function (same as categories)
  const generateSlug = (text: string) => {
    return text
      .toLowerCase()
      .replace(/[åä]/g, 'a') // replace Swedish characters å, ä with 'a'
      .replace(/ö/g, 'o') // replace Swedish character ö with 'o'
      .replace(/[^a-z0-9\s-]/g, '') // remove all non-alphanumeric chars
      .replace(/\s+/g, '-') // replace spaces with hyphens
      .replace(/-+/g, '-') // replace multiple hyphens with a single one
      .trim();
  };

  // Auto-generate slug when name changes
  useEffect(() => {
    if (name && !slug) {
      const newSlug = generateSlug(name);
      setSlug(newSlug);
      onFormDataChange?.('slug', newSlug);
    }
  }, [name, slug, onFormDataChange]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
    onFormDataChange?.('name', newName);

    // Auto-generate slug if current slug is empty or matches generated slug
    if (!slug || slug === generateSlug(name)) {
      const newSlug = generateSlug(newName);
      setSlug(newSlug);
      onFormDataChange?.('slug', newSlug);
    }
  };

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSlug = e.target.value;
    setSlug(newSlug);
    onFormDataChange?.('slug', newSlug);
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setDescription(newDescription);
    onFormDataChange?.('description', newDescription);
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Information</CardTitle>
        <CardDescription>Essential product details</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Product Name *</Label>
            <Input
              id="name"
              name="name"
              value={name}
              onChange={handleNameChange}
              required
              className={fieldErrors?.name ? "border-red-500" : ""}
            />
            {fieldErrors?.name && (
              <p className="text-sm text-red-600">{fieldErrors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">URL Slug *</Label>
            <Input
              id="slug"
              name="slug"
              value={slug}
              onChange={handleSlugChange}
              required
              className={fieldErrors?.slug ? "border-red-500" : ""}
            />
            {fieldErrors?.slug && (
              <p className="text-sm text-red-600">{fieldErrors.slug}</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            value={description}
            onChange={handleDescriptionChange}
            rows={4}
            placeholder="Detailed product description..."
            className={fieldErrors?.description ? "border-red-500" : ""}
          />
          {fieldErrors?.description && (
            <p className="text-sm text-red-600">{fieldErrors.description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}