import { prisma } from "@/lib/database";
import { notFound } from "next/navigation";
import EditCategoryForm from "./form";

interface EditCategoryPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditCategoryPage({ params }: EditCategoryPageProps) {
  const { id } = await params;
  
  const category = await prisma.category.findUnique({
    where: { id },
  });

  if (!category) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Edit Category</h1>
        <p className="text-gray-600 mt-2">Update category information</p>
      </div>
      <EditCategoryForm category={category} />
    </div>
  );
}
