import { getLegalPageBySlug } from "@/lib/actions/legal";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { LegalPageStatus } from "@/generated/prisma";
import { marked } from "marked";

export default async function AdminPreviewLegalPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const legalPage = await getLegalPageBySlug(slug);
  
  if (!legalPage) {
    notFound();
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-100 text-green-800';
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800';
      case 'REVIEW':
        return 'bg-blue-100 text-blue-800';
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto py-8">
      {/* Admin <PERSON>er */}
      <div className="bg-blue-50 border border-blue-200 text-blue-800 px-6 py-4 rounded-lg mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-medium">
                 Admin Preview Mode
              </h3>
              <div className="mt-1 text-sm">
                <p>You are viewing this page as an administrator. This view shows content regardless of publication status.</p>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Badge className={getStatusColor(legalPage.status)}>
              {legalPage.status}
            </Badge>
            <Badge variant="outline" className="font-mono">
              v{legalPage.version}
            </Badge>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button asChild variant="outline">
            <Link href="/admin/legal">← Back to List</Link>
          </Button>
          <Button asChild variant="default">
            <Link href={`/admin/legal/${legalPage.id}`}>Edit Page</Link>
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          {legalPage.status === LegalPageStatus.PUBLISHED && (
            <Button asChild variant="outline">
              <Link href={`/${legalPage.slug}`} target="_blank">
                View Public Page
              </Link>
            </Button>
          )}
        </div>
      </div>

      {/* Status-specific warnings */}
      {legalPage.status === LegalPageStatus.DRAFT && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-6 py-4 rounded-lg mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium">Draft Status</h3>
              <p className="text-sm mt-1">This page is in draft status and is not visible to the public.</p>
            </div>
          </div>
        </div>
      )}

      {legalPage.status === LegalPageStatus.REVIEW && (
        <div className="bg-blue-50 border border-blue-200 text-blue-800 px-6 py-4 rounded-lg mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium">Under Review</h3>
              <p className="text-sm mt-1">This page is under review. Public users see a &quot;coming soon&quot; message.</p>
            </div>
          </div>
        </div>
      )}

      {legalPage.status === LegalPageStatus.ARCHIVED && (
        <div className="bg-gray-50 border border-gray-200 text-gray-800 px-6 py-4 rounded-lg mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l4 4 4-4" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium">Archived</h3>
              <p className="text-sm mt-1">This page is archived and not accessible to the public.</p>
            </div>
          </div>
        </div>
      )}

      {/* Page Content */}
      <div className="bg-white rounded-lg shadow-sm border p-8">
        <div className="prose dark:prose-invert max-w-none">
          <div dangerouslySetInnerHTML={{ __html: marked(legalPage.content) }} />
        </div>
        
        <div className="mt-8 pt-6 border-t text-sm text-gray-500">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p><strong>Version:</strong> {legalPage.version}</p>
              <p><strong>Category:</strong> {legalPage.category.replace(/_/g, ' ')}</p>
              <p><strong>Slug:</strong> /{legalPage.slug}</p>
            </div>
            <div>
              <p><strong>Created:</strong> {new Date(legalPage.createdAt).toLocaleString()}</p>
              <p><strong>Last Updated:</strong> {new Date(legalPage.updatedAt).toLocaleString()}</p>
              {legalPage.effectiveDate && (
                <p><strong>Effective Date:</strong> {new Date(legalPage.effectiveDate).toLocaleString()}</p>
              )}
              {legalPage.expiryDate && (
                <p><strong>Expiry Date:</strong> {new Date(legalPage.expiryDate).toLocaleString()}</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Generate metadata for admin preview
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const legalPage = await getLegalPageBySlug(slug);
  
  if (!legalPage) {
    return {
      title: 'Page Not Found - Admin Preview',
    };
  }
  
  return {
    title: `[ADMIN PREVIEW] ${legalPage.title}`,
    description: `Admin preview of ${legalPage.title} - Status: ${legalPage.status}`,
    robots: 'noindex, nofollow', // Always prevent indexing of admin pages
  };
}
