"use server";

import { prisma } from "@/lib/database";
import { redirect } from "next/navigation";
import { z } from "zod";
import { handleActionError, createErrorResult, type ActionResult } from "@/lib/error-utils";

// Reuse the same validation schema as create
const updateProductSchema = z.object({
  id: z.string().min(1, "Product ID is required"),
  name: z.string().min(1, "Product name is required"),
  slug: z.string().min(1, "URL slug is required").regex(
    /^[a-z0-9-]+$/,
    "Slug can only contain lowercase letters, numbers, and hyphens"
  ),
  basePrice: z.string().min(1, "Price is required").pipe(
    z.coerce.number()
      .min(0, "Price must be 0 or greater")
      .refine((val) => {
        const rounded = Math.round(val * 100) / 100;
        return Math.abs(val - rounded) < 0.001;
      }, "Price can have at most 2 decimal places")
  ),
  sku: z.string().min(1, "SKU is required"),
  stock: z.string().min(1, "Stock is required").pipe(
    z.coerce.number().int().min(0, "Stock must be 0 or greater")
  ),
  categoryIds: z.array(z.string()).min(1, "At least one category is required"),
  
  // Optional fields
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  hasOption: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
  
  // Physical dimensions (optional)
  weight: z.coerce.number().optional(),
  length: z.coerce.number().optional(),
  width: z.coerce.number().optional(),
  heigth: z.coerce.number().optional(),
  
  // SEO fields (optional)
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
});

export type UpdateActionState = {
  message: string;
  error: string;
  fieldErrors: Record<string, string>;
};

// Helper function to convert ActionResult to legacy ActionState
function convertToLegacyState(result: ActionResult): UpdateActionState {
  if (result.success) {
    return {
      message: result.error || "Success",
      error: "",
      fieldErrors: {},
    };
  }
  
  // Convert fieldErrors from string[] to string
  const legacyFieldErrors: Record<string, string> = {};
  if (result.fieldErrors) {
    Object.entries(result.fieldErrors).forEach(([field, errors]) => {
      if (errors && errors.length > 0) {
        legacyFieldErrors[field] = errors[0];
      }
    });
  }
  
  return {
    message: "",
    error: result.error || "An error occurred",
    fieldErrors: legacyFieldErrors,
  };
}

export async function updateProduct(
  _prevState: UpdateActionState,
  formData: FormData
): Promise<UpdateActionState> {
  try {
    // Extract form data
    const rawData = Object.fromEntries(formData.entries());
    
    // Process the data
    const processedData: any = {
      ...rawData,
      // Parse JSON fields
      categoryIds: rawData.categoryIds ? JSON.parse(rawData.categoryIds as string) : [],
      tags: rawData.tags ? JSON.parse(rawData.tags as string) : [],
      // Convert checkboxes to booleans
      isActive: formData.has('isActive'),
      hasOption: formData.has('hasOption'),
    };

    // Validate the data
    const result = updateProductSchema.safeParse(processedData);
    
    // Collect all validation errors
    const allErrors: Record<string, string[]> = {};
    
    if (!result.success) {
      // Add schema validation errors
      const schemaErrors = result.error.flatten().fieldErrors;
      Object.entries(schemaErrors).forEach(([field, errors]) => {
        if (errors && errors.length > 0) {
          allErrors[field] = errors;
        }
      });
    }

    // Check uniqueness constraints (excluding current product)
    if (result.success || (processedData.slug && typeof processedData.slug === 'string')) {
      const existingSlug = await prisma.product.findFirst({
        where: { 
          slug: processedData.slug as string,
          NOT: { id: processedData.id as string }
        },
      });

      if (existingSlug) {
        allErrors.slug = allErrors.slug || [];
        allErrors.slug.push("This slug is already taken");
      }
    }

    if (result.success || (processedData.sku && typeof processedData.sku === 'string')) {
      const existingSku = await prisma.product.findFirst({
        where: { 
          sku: processedData.sku as string,
          NOT: { id: processedData.id as string }
        },
      });

      if (existingSku) {
        allErrors.sku = allErrors.sku || [];
        allErrors.sku.push("This SKU is already taken");
      }
    }

    // If we have any errors, return them all
    if (Object.keys(allErrors).length > 0) {
      const errorResult = createErrorResult(
        "Please fix all errors below and try again.",
        allErrors
      );
      return convertToLegacyState(errorResult);
    }

    if (!result.success) {
      const errorResult = handleActionError(result.error);
      return convertToLegacyState(errorResult);
    }

    const data = result.data;

    // Update the product
    await prisma.product.update({
      where: { id: data.id },
      data: {
        name: data.name,
        slug: data.slug,
        description: data.description || null,
        basePrice: data.basePrice,
        sku: data.sku,
        stock: data.stock,
        isActive: data.isActive,
        hasOption: data.hasOption,
        weight: data.weight || null,
        length: data.length || null,
        width: data.width || null,
        heigth: data.heigth || null,
        metaTitle: data.metaTitle || null,
        metaDescription: data.metaDescription || null,
        tags: data.tags || [],
        categories: {
          set: [], // Clear existing connections
          connect: data.categoryIds.map((id: string) => ({ id })),
        },
      },
    });

  } catch (error) {
    // Use centralized error handling
    const errorResult = handleActionError(error);
    return convertToLegacyState(errorResult);
  }

  // Success - redirect to products list
  redirect("/admin/products");
}
