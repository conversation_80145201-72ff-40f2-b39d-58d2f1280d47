generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String          @id
  name          String
  email         String          @unique
  emailVerified Boolean
  image         String?
  createdAt     DateTime
  updatedAt     DateTime
  role          String?
  banned        Boolean?
  banReason     String?
  banExpires    DateTime?
  accounts      Account[]
  reviews       ProductReview[]
  sessions      Session[]

  @@map("user")
}

model Session {
  id             String   @id
  expiresAt      DateTime
  token          String   @unique
  createdAt      DateTime
  updatedAt      DateTime
  ipAddress      String?
  userAgent      String?
  userId         String
  impersonatedBy String?
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model Product {
  id              String           @id @default(cuid())
  name            String
  description     String?
  slug            String           @unique
  basePrice       Decimal          @db.Decimal(10, 2)
  sku             String           @unique
  stock           Int              @default(0)
  isActive        Boolean          @default(true)
  hasOption       Boolean          @default(false)
  weight          Decimal?         @db.Decimal(8, 3)
  length          Decimal?         @db.Decimal(8, 2)
  width           Decimal?         @db.Decimal(8, 2)
  heigth          Decimal?         @db.Decimal(8, 2)
  metaTitle       String?
  metaDescription String?
  tags            String[]
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  images          ProductImage[]
  productOptions  ProductOption[]
  reviews         ProductReview[]
  variants        ProductVariant[]
  categories      Category[]       @relation("CategoryToProduct")

  @@map("products")
}

model Category {
  id          String    @id @default(cuid())
  name        String
  description String?
  slug        String    @unique
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[] @relation("CategoryToProduct")

  @@map("categories")
}

model OptionType {
  id             String          @id @default(cuid())
  name           String          @unique
  description    String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  optionValues   OptionValue[]
  productOptions ProductOption[]

  @@map("optiontypes")
}

model OptionValue {
  id            String                  @id @default(cuid())
  value         String
  optionTypeId  String
  createdAt     DateTime                @default(now())
  updatedAt     DateTime                @updatedAt
  optionType    OptionType              @relation(fields: [optionTypeId], references: [id], onDelete: Cascade)
  variantOption ProductVariantOptions[]

  @@unique([optionTypeId, value])
  @@map("optionvalues")
}

model ProductOption {
  id           String     @id @default(cuid())
  productId    String
  optionTypeId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  optionType   OptionType @relation(fields: [optionTypeId], references: [id], onDelete: Cascade)
  product      Product    @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, optionTypeId])
  @@map("productoptions")
}

model ProductVariant {
  id              String                  @id @default(cuid())
  name            String
  basePrice       Decimal                 @db.Decimal(10, 2)
  sku             String                  @unique
  stock           Int                     @default(0)
  isActive        Boolean                 @default(true)
  hasOption       Boolean                 @default(false)
  weight          Decimal?                @db.Decimal(8, 3)
  length          Decimal?                @db.Decimal(8, 2)
  width           Decimal?                @db.Decimal(8, 2)
  heigth          Decimal?                @db.Decimal(8, 2)
  metaTitle       String?
  metaDescription String?
  productId       String
  createdAt       DateTime                @default(now())
  updatedAt       DateTime                @updatedAt
  images          ProductVariantImage[]
  variantOption   ProductVariantOptions[]
  product         Product                 @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("productvariants")
}

model ProductVariantOptions {
  id            String         @id @default(cuid())
  variantId     String
  optionValueId String
  optionValue   OptionValue    @relation(fields: [optionValueId], references: [id], onDelete: Cascade)
  variant       ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([variantId, optionValueId])
  @@map("productvariantoption")
}

model ProductImage {
  id        String   @id @default(cuid())
  url       String
  altText   String?
  sortOrder Int      @default(0)
  isMain    Boolean  @default(false)
  productId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("productimages")
}

model ProductVariantImage {
  id        String         @id @default(cuid())
  url       String
  altText   String?
  sortOrder Int            @default(0)
  isMain    Boolean        @default(false)
  variantId String
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt
  variant   ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("productvariantimages")
}

model ProductReview {
  id         String   @id @default(cuid())
  rating     Int
  title      String?
  comment    String?
  userId     String
  isApproved Boolean  @default(true)
  productId  String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  customer   User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("productreviews")
}

model LegalPage {
  id              String              @id @default(cuid())
  title           String
  slug            String              @unique
  content         String
  version         String              @default("1.0")
  status          LegalPageStatus     @default(DRAFT)
  effectiveDate   DateTime?
  expiryDate      DateTime?
  category        LegalPageCategory
  metaTitle       String?
  metaDescription String?
  lastReviewedAt  DateTime?
  reviewedBy      String?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  // Relations
  history         LegalPageHistory[]
}

model LegalPageHistory {
  id              String            @id @default(cuid())
  legalPageId     String
  title           String
  slug            String
  content         String
  version         String
  status          LegalPageStatus
  effectiveDate   DateTime?
  expiryDate      DateTime?
  category        LegalPageCategory
  metaTitle       String?
  metaDescription String?
  changeReason    String?
  changedBy       String?
  createdAt       DateTime          @default(now())

  // Relations
  legalPage       LegalPage         @relation(fields: [legalPageId], references: [id], onDelete: Cascade)

  @@index([legalPageId])
  @@index([version])
}

model LegalPageTemplate {
  id          String            @id @default(cuid())
  name        String            @unique
  description String?
  category    LegalPageCategory
  content     String
  sections    Json?
  isActive    Boolean           @default(true)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
}

enum LegalPageStatus {
  DRAFT
  REVIEW
  PUBLISHED
  ARCHIVED
}

enum LegalPageCategory {
  TERMS_OF_SERVICE
  PRIVACY_POLICY
  DELIVERY_TERMS
  RETURN_POLICY
  ABOUT_US
  RIGHT_OF_WITHDRAWAL
  COOKIE_POLICY
  OTHER
}
