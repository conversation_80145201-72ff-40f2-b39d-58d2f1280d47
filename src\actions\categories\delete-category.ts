"use server";

import { prisma } from "@/lib/database";
import { revalidatePath } from "next/cache";


export async function deleteCategory(categoryId: string) {
  try {
 // check if category has products
 const productsCount = await prisma.product.count({
   where: {
     categories: {
       some: {id: categoryId }
     }
   }
 });

 if (productsCount > 0) {
   return {
     success: false,
     error: `Cannot delete category. It has ${productsCount} product(s) assigned to it.`
   };
 }

    await prisma.category.delete({
      where: {
        id: categoryId,
      },
    });

      revalidatePath("/admin/categories");
  return { success: true };
} catch (error) {
  console.error("Database error:", error);
  return {
    success: false,
    error: "An error occurred while deleting the category."
  };
}
}
