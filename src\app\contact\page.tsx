import { ContactForm } from "@/components/contact/contact-form";
import { Separator } from "@/components/ui/separator";
import { Phone, Mail, MapPin } from "lucide-react";

export default function ContactPage() {
  return (
    <div className="container mx-auto py-12 px-4 max-w-5xl">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Contact Us</h1>
        <p className="text-lg text-muted-foreground">
          We&apos;re happy to help with questions about products, orders and support
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-16">
        {/* Contact Information - Left Side */}
        <div className="space-y-10">
          <div>
            <Separator className="mb-6"/>
            <div className="space-y-8">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <a href="tel:08-501-183-99" className="text-lg hover:underline decoration-1 underline-offset-2">
                    08-501 183 99
                  </a>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <a href="mailto:<EMAIL>" className="text-lg hover:underline decoration-1 underline-offset-2">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-start gap-3">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div className="text-muted-foreground">
                    <p>Luxe Group AB</p>
                    <p>Storgatan 123, 721 30 Västerås, Sweden</p>
                  </div>
                </div>
              </div>

              <Separator className="my-8" />

              <div>
                <h3 className="font-medium mb-4">Opening Hours</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Monday</span>
                    <span className="font-medium">09:00 - 17:00</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Tuesday</span>
                    <span className="font-medium">09:00 - 17:00</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Wednesday</span>
                    <span className="font-medium">09:00 - 17:00</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Thursday</span>
                    <span className="font-medium">09:00 - 17:00</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Friday</span>
                    <span className="font-medium">09:00 - 17:00</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Weekends</span>
                    <span className="font-medium">Closed</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Form - Right Side */}
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold mb-2 text-foreground">Send Message</h2>
            <p className="text-muted-foreground mb-6">
              Fill out the form and we&apos;ll get back to you as soon as possible
            </p>
          </div>

          <ContactForm />
        </div>
      </div>
    </div>
  );
}
