"use server";

import { prisma } from "@/lib/database";
import { z } from "zod";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";

export type ActionState = {
  message: string;
  error: string;
  fieldErrors: Record<string, string>;
}

const optionTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
});

export async function createOptionType(prevState: ActionState, formData: FormData): Promise<ActionState> {
  const rawData = Object.fromEntries(formData.entries());
  
  // Parse option values from form data
  const optionValues: string[] = [];
  let index = 0;
  while (formData.has(`optionValue_${index}`)) {
    const value = formData.get(`optionValue_${index}`) as string;
    if (value.trim()) {
      optionValues.push(value.trim());
    }
    index++;
  }

  const processedData = {
    ...rawData,
    optionValues,
  };

  const result = optionTypeSchema.safeParse(processedData);
  
  if (!result.success) {
    const fieldErrors: Record<string, string> = {};
    Object.entries(result.error.flatten().fieldErrors).forEach(([field, errors]) => {
      if (errors && errors.length > 0) {
        fieldErrors[field] = errors[0];
      }
    });

    return {
      message: "",
      error: "Please fix the errors below and try again.",
      fieldErrors,
    };
  }

  if (optionValues.length === 0) {
    return {
      message: "",
      error: "At least one option value is required.",
      fieldErrors: {},
    };
  }

  const data = result.data;

  try {
    // check if name already exists
    const existingOptionType = await prisma.optionType.findUnique({
      where: { name: data.name },
    });

    if (existingOptionType) {
      return {
        message: "",
        error: "An option type with this name already exists.",
        fieldErrors: { name: "This name is already taken" },
      };
    }

    await prisma.optionType.create({
      data: {
        name: data.name,
        description: data.description,
        optionValues: {
          create: optionValues.map(value => ({ value }))
        }
      },
    });

    revalidatePath("/admin/option-types");
  } catch (error) {
    console.error("Database error:", error);
    return {
      message: "",
      error: "An error occurred while creating the option type.",
      fieldErrors: {},
    };
  }

  redirect("/admin/option-types");
}
