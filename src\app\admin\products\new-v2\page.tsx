import { prisma } from "@/lib/database";
import CreateProductFormV2 from "./form";

export default async function CreateProductPageV2() {
  // Fetch categories for the form
  const categories = await prisma.category.findMany({
    select: {
      id: true,
      name: true,
      slug: true,
    },
    orderBy: {
      name: 'asc',
    },
  });

  return (
    <div className="container mx-auto py-6">
      
      <CreateProductFormV2 categories={categories} />
    </div>
  );
}
