import { prisma } from "@/lib/database";
import CreateProductFormV2 from "./form";

export default async function CreateProductPageV2() {
  // Fetch categories for the form
  const categories = await prisma.category.findMany({
    select: {
      id: true,
      name: true,
      slug: true,
    },
    orderBy: {
      name: 'asc',
    },
  });

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Create Product (Simplified V2)</h1>
        <p className="text-gray-600 mt-2">
          Create a new product with all essential information in one compact form.
        </p>
      </div>
      
      <CreateProductFormV2 categories={categories} />
    </div>
  );
}
