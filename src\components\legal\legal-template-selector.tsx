"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { createLegalPageFromTemplate } from "@/lib/actions/legal";
import { LegalPageStatus } from "@/generated/prisma";

interface Template {
  id: string;
  name: string;
  description: string | null;
  category: string;
  content: string;
  isActive: boolean;
}

interface LegalTemplateSelectorProps {
  templates: Template[];
  onSuccess?: () => void;
}

export function LegalTemplateSelector({ templates, onSuccess }: LegalTemplateSelectorProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string[]>>({});
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const router = useRouter();

  async function handleCreateFromTemplate(formData: FormData) {
    if (!selectedTemplate) return;

    setIsSubmitting(true);
    setErrors({});

    const data = {
      title: formData.get("title") as string,
      slug: formData.get("slug") as string,
      status: formData.get("status") as LegalPageStatus || "DRAFT",
      metaTitle: formData.get("metaTitle") as string || undefined,
      metaDescription: formData.get("metaDescription") as string || undefined,
    };

    const result = await createLegalPageFromTemplate(selectedTemplate.id, data);

    if (result.success) {
      toast.success(result.error || "Legal page created from template successfully!");
      setIsDialogOpen(false);
      setSelectedTemplate(null);
      if (onSuccess) {
        onSuccess();
      } else {
        router.refresh();
      }
    } else {
      toast.error(result.error || "Failed to create legal page from template");
      if (result.fieldErrors) {
        setErrors(result.fieldErrors);
      }
    }

    setIsSubmitting(false);
  }

  const formatCategory = (category: string) => {
    return category.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create from Template</CardTitle>
        <p className="text-sm text-muted-foreground">
          Start with a pre-built template to save time
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {templates.map((template) => (
            <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">{template.name}</CardTitle>
                  <Badge variant="outline" className="text-xs">
                    {formatCategory(template.category)}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  {template.description || "No description available"}
                </p>
              </CardHeader>
              <CardContent className="pt-0">
                <Dialog open={isDialogOpen && selectedTemplate?.id === template.id} onOpenChange={setIsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full"
                      onClick={() => setSelectedTemplate(template)}
                    >
                      Use Template
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Create from {template.name}</DialogTitle>
                      <DialogDescription>
                        Customize the details for your new legal page based on this template.
                      </DialogDescription>
                    </DialogHeader>
                    
                    <form action={handleCreateFromTemplate} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="title">Title</Label>
                          <Input
                            id="title"
                            name="title"
                            defaultValue={template.name.replace(" Template", "")}
                            required
                            className={errors.title ? "border-red-500" : ""}
                          />
                          {errors.title && (
                            <p className="text-sm text-red-500">{errors.title[0]}</p>
                          )}
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="slug">Slug</Label>
                          <Input
                            id="slug"
                            name="slug"
                            placeholder="e.g. terms-of-service"
                            required
                            className={errors.slug ? "border-red-500" : ""}
                          />
                          {errors.slug && (
                            <p className="text-sm text-red-500">{errors.slug[0]}</p>
                          )}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="status">Initial Status</Label>
                        <Select name="status" defaultValue="DRAFT">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="DRAFT">Draft - Work in progress</SelectItem>
                            <SelectItem value="REVIEW">Review - Coming soon</SelectItem>
                            <SelectItem value="PUBLISHED">Published - Live immediately</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="metaTitle">Meta Title (SEO)</Label>
                          <Input
                            id="metaTitle"
                            name="metaTitle"
                            placeholder="SEO title"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="metaDescription">Meta Description (SEO)</Label>
                          <Input
                            id="metaDescription"
                            name="metaDescription"
                            placeholder="SEO description"
                          />
                        </div>
                      </div>

                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Label className="text-sm font-medium">Template Preview:</Label>
                        <div className="mt-2 text-sm text-gray-600 max-h-32 overflow-y-auto">
                          <div className="text-xs prose prose-sm">
                            {template.content.substring(0, 300).split('\n').map((line, i) => (
                              <div key={i}>
                                {line.startsWith('# ') ? (
                                  <strong className="text-lg">{line.replace('# ', '')}</strong>
                                ) : line.startsWith('## ') ? (
                                  <strong className="text-base">{line.replace('## ', '')}</strong>
                                ) : (
                                  line
                                )}
                              </div>
                            ))}
                            ...
                          </div>
                        </div>
                      </div>

                      <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button type="submit" disabled={isSubmitting}>
                          {isSubmitting ? "Creating..." : "Create Legal Page"}
                        </Button>
                      </DialogFooter>
                    </form>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {templates.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>No templates available. Templates will be added soon!</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
