-- CreateTable
CREATE TABLE "LegalPageHistory" (
    "id" TEXT NOT NULL,
    "legalPageId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "status" "LegalPageStatus" NOT NULL,
    "effectiveDate" TIMESTAMP(3),
    "expiryDate" TIMESTAMP(3),
    "category" "LegalPageCategory" NOT NULL,
    "metaTitle" TEXT,
    "metaDescription" TEXT,
    "changeReason" TEXT,
    "changedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "LegalPageHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LegalPageTemplate" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" "LegalPageCategory" NOT NULL,
    "content" TEXT NOT NULL,
    "sections" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LegalPageTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "LegalPageHistory_legalPageId_idx" ON "LegalPageHistory"("legalPageId");

-- CreateIndex
CREATE INDEX "LegalPageHistory_version_idx" ON "LegalPageHistory"("version");

-- CreateIndex
CREATE UNIQUE INDEX "LegalPageTemplate_name_key" ON "LegalPageTemplate"("name");

-- AddForeignKey
ALTER TABLE "LegalPageHistory" ADD CONSTRAINT "LegalPageHistory_legalPageId_fkey" FOREIGN KEY ("legalPageId") REFERENCES "LegalPage"("id") ON DELETE CASCADE ON UPDATE CASCADE;
