"use client";

import { useState } from "react";
import { ProductCard } from "./ProductCard";
import { ProductListItem } from "@/actions/products/get-products";
import { deleteProduct, toggleProductStatus } from "@/actions/products/delete-product";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Filter, Grid, List, Plus } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface ProductListProps {
  products: ProductListItem[];
  categories: Array<{
    id: string;
    name: string;
    _count: { products: number };
  }>;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  filters: {
    search: string;
    categoryId: string;
    status: string;
    sortBy: string;
    sortOrder: string;
  };
}

export function ProductList({ products, categories, pagination, filters }: ProductListProps) {
  const router = useRouter();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const handleSearch = (search: string) => {
    const params = new URLSearchParams();
    if (search) params.set('search', search);
    if (filters.categoryId) params.set('categoryId', filters.categoryId);
    if (filters.status !== 'all') params.set('status', filters.status);
    if (filters.sortBy !== 'createdAt') params.set('sortBy', filters.sortBy);
    if (filters.sortOrder !== 'desc') params.set('sortOrder', filters.sortOrder);
    
    router.push(`/admin/products?${params.toString()}`);
  };

  const handleFilter = (key: string, value: string) => {
    const params = new URLSearchParams();
    if (filters.search) params.set('search', filters.search);
    if (key === 'categoryId' && value) params.set('categoryId', value);
    else if (filters.categoryId && key !== 'categoryId') params.set('categoryId', filters.categoryId);
    
    if (key === 'status' && value !== 'all') params.set('status', value);
    else if (filters.status !== 'all' && key !== 'status') params.set('status', filters.status);
    
    if (key === 'sortBy' && value !== 'createdAt') params.set('sortBy', value);
    else if (filters.sortBy !== 'createdAt' && key !== 'sortBy') params.set('sortBy', filters.sortBy);
    
    if (key === 'sortOrder' && value !== 'desc') params.set('sortOrder', value);
    else if (filters.sortOrder !== 'desc' && key !== 'sortOrder') params.set('sortOrder', filters.sortOrder);
    
    router.push(`/admin/products?${params.toString()}`);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this product?')) return;
    
    setIsDeleting(id);
    try {
      const result = await deleteProduct(id);
      if (result.success) {
        toast.success(result.message);
        router.refresh();
      } else {
        toast.error(result.error);
      }
    } catch (error) {
      toast.error('Failed to delete product');
    } finally {
      setIsDeleting(null);
    }
  };

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const result = await toggleProductStatus(id, isActive);
      if (result.success) {
        toast.success(result.message);
        router.refresh();
      } else {
        toast.error(result.error);
      }
    } catch (error) {
      toast.error('Failed to update product status');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Products</h1>
          <p className="text-gray-600 mt-1">
            {pagination.total} product{pagination.total !== 1 ? 's' : ''} total
          </p>
        </div>
        <Link href="/admin/products/new-v2">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white border rounded-lg p-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div>
            <Label htmlFor="search">Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="search"
                placeholder="Search products..."
                defaultValue={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div>
            <Label>Category</Label>
            <Select value={filters.categoryId} onValueChange={(value) => handleFilter('categoryId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name} ({category._count.products})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div>
            <Label>Status</Label>
            <Select value={filters.status} onValueChange={(value) => handleFilter('status', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Sort */}
          <div>
            <Label>Sort by</Label>
            <Select value={`${filters.sortBy}-${filters.sortOrder}`} onValueChange={(value) => {
              const [sortBy, sortOrder] = value.split('-');
              handleFilter('sortBy', sortBy);
              handleFilter('sortOrder', sortOrder);
            }}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">Newest first</SelectItem>
                <SelectItem value="createdAt-asc">Oldest first</SelectItem>
                <SelectItem value="name-asc">Name A-Z</SelectItem>
                <SelectItem value="name-desc">Name Z-A</SelectItem>
                <SelectItem value="price-asc">Price low to high</SelectItem>
                <SelectItem value="price-desc">Price high to low</SelectItem>
                <SelectItem value="stock-asc">Stock low to high</SelectItem>
                <SelectItem value="stock-desc">Stock high to low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Products Grid/List */}
      {products.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No products found</p>
          <p className="text-gray-400 mt-2">Try adjusting your search or filters</p>
        </div>
      ) : (
        <div className={viewMode === 'grid' 
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" 
          : "space-y-4"
        }>
          {products.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              onDelete={handleDelete}
              onToggleStatus={handleToggleStatus}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={page === pagination.page ? 'default' : 'outline'}
              size="sm"
              onClick={() => {
                const params = new URLSearchParams();
                if (filters.search) params.set('search', filters.search);
                if (filters.categoryId) params.set('categoryId', filters.categoryId);
                if (filters.status !== 'all') params.set('status', filters.status);
                if (filters.sortBy !== 'createdAt') params.set('sortBy', filters.sortBy);
                if (filters.sortOrder !== 'desc') params.set('sortOrder', filters.sortOrder);
                params.set('page', page.toString());
                router.push(`/admin/products?${params.toString()}`);
              }}
            >
              {page}
            </Button>
          ))}
        </div>
      )}
    </div>
  );
}
