"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";

interface VersionHelperProps {
  currentVersion?: string;
  onVersionChange: (version: string) => void;
}

export function VersionHelper({ currentVersion = "1.0", onVersionChange }: VersionHelperProps) {
  const [version, setVersion] = useState(currentVersion);

  useEffect(() => {
    setVersion(currentVersion);
  }, [currentVersion]);

  const incrementMajor = () => {
    const [major] = version.split('.').map(Number);
    const newVersion = `${major + 1}.0`;
    setVersion(newVersion);
    onVersionChange(newVersion);
  };

  const incrementMinor = () => {
    const [major, minor] = version.split('.').map(Number);
    const newVersion = `${major}.${minor + 1}`;
    setVersion(newVersion);
    onVersionChange(newVersion);
  };

  return (
    <div className="flex items-center space-x-2">
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={incrementMinor}
        className="text-xs"
      >
        +0.1
      </Button>
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={incrementMajor}
        className="text-xs"
      >
        +1.0
      </Button>
    </div>
  );
}
