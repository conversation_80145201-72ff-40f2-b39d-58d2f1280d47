import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";

interface MetaInfoStepProps {
  fieldErrors?: Record<string, string>;
  formData?: {
    metaTitle: string;
    metaDescription: string;
  };
  onFormDataChange?: (field: string, value: string) => void;
}

export default function MetaInfoStep({
  fieldErrors,
  formData,
  onFormDataChange
}: MetaInfoStepProps) {
  const [metaTitle, setMetaTitle] = useState(formData?.metaTitle || "");
  const [metaDescription, setMetaDescription] = useState(formData?.metaDescription || "");

  const handleMetaTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setMetaTitle(newTitle);
    onFormDataChange?.('metaTitle', newTitle);
  };

  const handleMetaDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setMetaDescription(newDescription);
    onFormDataChange?.('metaDescription', newDescription);
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>SEO & Meta Information</CardTitle>
        <CardDescription>Search engine optimization settings</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="metaTitle">Meta Title</Label>
          <Input
            id="metaTitle"
            name="metaTitle"
            value={metaTitle}
            onChange={handleMetaTitleChange}
            placeholder="SEO title for search engines"
            className={fieldErrors?.metaTitle ? "border-red-500" : ""}
          />
          {fieldErrors?.metaTitle && (
            <p className="text-sm text-red-600">{fieldErrors.metaTitle}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="metaDescription">Meta Description</Label>
          <Textarea
            id="metaDescription"
            name="metaDescription"
            value={metaDescription}
            onChange={handleMetaDescriptionChange}
            rows={3}
            placeholder="Brief description for search engine results (150-160 characters recommended)"
            className={fieldErrors?.metaDescription ? "border-red-500" : ""}
          />
          {fieldErrors?.metaDescription && (
            <p className="text-sm text-red-600">{fieldErrors.metaDescription}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}