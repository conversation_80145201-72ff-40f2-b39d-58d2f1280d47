import { getLegalPageBySlug } from "@/lib/actions/legal";
import { notFound } from "next/navigation";
import { LegalPageStatus } from "@/generated/prisma";
import { marked } from "marked";

export default async function LegalPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const legalPage = await getLegalPageBySlug(slug);
  
  // If page doesn't exist, is archived, or is draft, return 404
  if (!legalPage ||
      legalPage.status === LegalPageStatus.ARCHIVED ||
      legalPage.status === LegalPageStatus.DRAFT) {
    notFound();
  }

  // If page is scheduled for future publication, check if it's time
  if (legalPage.status === LegalPageStatus.PUBLISHED &&
      legalPage.effectiveDate &&
      new Date(legalPage.effectiveDate) > new Date()) {
    notFound();
  }

  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      {/* Coming Soon banner for REVIEW status */}
      {legalPage.status === LegalPageStatus.REVIEW && (
        <div className="bg-blue-50 border border-blue-200 text-blue-800 px-6 py-4 rounded-lg mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium">
                 Coming Soon!
              </h3>
              <div className="mt-1 text-sm">
                <p>This page is currently under review and will be published soon. Thank you for your patience!</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">{legalPage.title}</h1>
        {legalPage.status === LegalPageStatus.REVIEW && (
          <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            Coming Soon
          </span>
        )}
      </div>
      
      {/* Only show content for PUBLISHED pages */}
      {legalPage.status === LegalPageStatus.PUBLISHED && (
        <>
          <div className="prose dark:prose-invert max-w-none">
            {/* Render Markdown content as HTML */}
            <div dangerouslySetInnerHTML={{ __html: marked(legalPage.content) }} />
          </div>

          <div className="mt-8 text-sm text-gray-500">
            <p>Version: {legalPage.version}</p>
            {legalPage.effectiveDate && (
              <p>Effective Date: {new Date(legalPage.effectiveDate).toLocaleDateString()}</p>
            )}
          </div>
        </>
      )}

      {/* For REVIEW status, show additional information instead of content */}
      {legalPage.status === LegalPageStatus.REVIEW && (
        <div className="mt-8 text-center">
          <div className="bg-gray-50 rounded-lg p-8">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Content Under Review</h3>
            <p className="text-gray-600 max-w-md mx-auto">
              We&apos;re putting the finishing touches on this page. Check back soon for the complete content!
            </p>
            <div className="mt-6 text-sm text-gray-500">
              <p>Category: {legalPage.category.replace(/_/g, ' ')}</p>
              <p>Version: {legalPage.version}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const legalPage = await getLegalPageBySlug(slug);

  if (!legalPage ||
      legalPage.status === LegalPageStatus.ARCHIVED ||
      legalPage.status === LegalPageStatus.DRAFT) {
    return {
      title: 'Page Not Found',
    };
  }

  // Add status indicator to title for review pages
  let title = legalPage.metaTitle || legalPage.title;
  if (legalPage.status === LegalPageStatus.REVIEW) {
    title = `${title} - Coming Soon`;
  }

  return {
    title,
    description: legalPage.status === LegalPageStatus.REVIEW
      ? `${legalPage.title} - Coming soon! This page is currently under review.`
      : legalPage.metaDescription || `${legalPage.title} - Legal Information`,
    // Prevent indexing of review pages
    robots: legalPage.status === LegalPageStatus.PUBLISHED ? undefined : 'noindex, nofollow',
  };
}