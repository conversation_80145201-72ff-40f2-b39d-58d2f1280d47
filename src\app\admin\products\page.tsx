import { getProducts, getProductCategories } from "@/actions/products/get-products";
import { ProductList } from "@/components/admin-product-crud/products/ProductList";
import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";

interface ProductsPageProps {
  searchParams: Promise<{
    search?: string;
    categoryId?: string;
    status?: 'active' | 'inactive' | 'all';
    sortBy?: 'name' | 'price' | 'createdAt' | 'stock';
    sortOrder?: 'asc' | 'desc';
    page?: string;
    pageSize?: string;
  }>;
}

function ProductListSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-32 mt-2" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Filters Skeleton */}
      <div className="bg-white border rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i}>
              <Skeleton className="h-4 w-16 mb-2" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </div>
      </div>

      {/* Products Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="border rounded-lg p-4">
            <Skeleton className="h-6 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2 mb-4" />
            <Skeleton className="h-8 w-24 mb-4" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

async function ProductsContent({ searchParams }: ProductsPageProps) {
  const params = await searchParams;
  
  const filters = {
    search: params.search || '',
    categoryId: params.categoryId || '',
    status: params.status || 'all' as const,
    sortBy: params.sortBy || 'createdAt' as const,
    sortOrder: params.sortOrder || 'desc' as const,
    page: parseInt(params.page || '1'),
    pageSize: parseInt(params.pageSize || '12'),
  };

  try {
    const [productsData, categories] = await Promise.all([
      getProducts(filters),
      getProductCategories(),
    ]);

    return (
      <ProductList
        products={productsData.products}
        categories={categories}
        pagination={productsData.pagination}
        filters={{
          search: filters.search,
          categoryId: filters.categoryId,
          status: filters.status,
          sortBy: filters.sortBy,
          sortOrder: filters.sortOrder,
        }}
      />
    );
  } catch (error) {
    console.error('Error loading products:', error);
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Error Loading Products
        </h2>
        <p className="text-gray-600 mb-4">
          There was an error loading the products. Please try again.
        </p>
        <button 
          onClick={() => window.location.reload()} 
          className="text-blue-600 hover:text-blue-800"
        >
          Reload Page
        </button>
      </div>
    );
  }
}

export default function ProductsPage(props: ProductsPageProps) {
  return (
    <div className="container mx-auto py-6">
      <Suspense fallback={<ProductListSkeleton />}>
        <ProductsContent {...props} />
      </Suspense>
    </div>
  );
}
