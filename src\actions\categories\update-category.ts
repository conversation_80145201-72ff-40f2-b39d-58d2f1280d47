"use server";

import { prisma } from "@/lib/database";
import { z } from "zod";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";

export type ActionState = {
  message: string;
  error: string;
  fieldErrors: Record<string, string>;
}
const categorySchema = z.object({
  name: z.string().min(1, "Name is required"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
  description: z.string().optional(),
});
export async function updateCategory(id: string, prevState: ActionState, formData: FormData): Promise<ActionState> {
  const rawData = Object.fromEntries(formData.entries());
  
  const result = categorySchema.safeParse(rawData);
  
  if (!result.success) {
    const fieldErrors: Record<string, string> = {};
    Object.entries(result.error.flatten().fieldErrors).forEach(([field, errors]) => {
      if (errors && errors.length > 0) {
        fieldErrors[field] = errors[0];
      }
    });

    return {
      message: "",
      error: "Please fix the errors below and try again.",
      fieldErrors,
    };
  }

  const data = result.data;

  try {
    // check if slug already exists (excluding current category)
    const existingCategory = await prisma.category.findFirst({
      where: { 
        slug: data.slug,
        NOT: { id }
      },
    });

    if (existingCategory) {
      return {
        message: "",
        error: "A category with this slug already exists.",
        fieldErrors: { slug: "This slug is already taken" },
      };
    }

    await prisma.category.update({
      where: { id },
      data: {
        name: data.name,
        slug: data.slug,
        description: data.description,
      },
    });

    revalidatePath("/admin/categories");
  } catch (error) {
    console.error("Database error:", error);
    return {
      message: "",
      error: "An error occurred while updating the category.",
      fieldErrors: {},
    };
  }

  redirect("/admin/categories");
}
