import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";

interface ProductOptionsStepProps {
  hasOption: boolean;
  setHasOption: (value: boolean) => void;
}

export default function ProductOptionsStep({
  hasOption,
  setHasOption
}: ProductOptionsStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Product Options</CardTitle>
        <CardDescription>Configure variants and options</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="hasOption"
            checked={hasOption}
            onCheckedChange={(checked) => setHasOption(checked === true)}
          />
          <Label htmlFor="hasOption">This product has variants/options</Label>
        </div>

        {hasOption && (
          <div className="p-4 border rounded-lg bg-blue-50">
            <p className="text-sm text-blue-800 mb-2">
              <strong>Note:</strong> Variant management will be available after creating the base product.
            </p>
            <p className="text-sm text-blue-700">
              You&apos;ll be able to add options like size, color, etc., and create specific variants with their own pricing and inventory.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}