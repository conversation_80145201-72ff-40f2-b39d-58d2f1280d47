"use server";

import { prisma } from "@/lib/database";

export interface ProductFilters {
  search?: string;
  categoryId?: string;
  status?: 'active' | 'inactive' | 'all';
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'name' | 'price' | 'createdAt' | 'stock';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}

export interface ProductListItem {
  id: string;
  name: string;
  slug: string;
  basePrice: number;
  sku: string;
  stock: number;
  isActive: boolean;
  hasOption: boolean;
  createdAt: Date;
  updatedAt: Date;
  categories: {
    id: string;
    name: string;
    slug: string;
  }[];
  _count: {
    variants: number;
    images: number;
  };
}

export interface ProductsResponse {
  products: ProductListItem[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export async function getProducts(filters: ProductFilters = {}): Promise<ProductsResponse> {
  const {
    search = '',
    categoryId,
    status = 'all',
    minPrice,
    maxPrice,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    page = 1,
    pageSize = 10
  } = filters;

  // Build where clause
  const where: any = {};

  // Search filter
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
      { sku: { contains: search, mode: 'insensitive' } },
    ];
  }

  // Status filter
  if (status !== 'all') {
    where.isActive = status === 'active';
  }

  // Category filter
  if (categoryId) {
    where.categories = {
      some: {
        id: categoryId
      }
    };
  }

  // Price range filter
  if (minPrice !== undefined || maxPrice !== undefined) {
    where.basePrice = {};
    if (minPrice !== undefined) where.basePrice.gte = minPrice;
    if (maxPrice !== undefined) where.basePrice.lte = maxPrice;
  }

  // Build orderBy clause
  const orderBy: any = {};
  if (sortBy === 'price') {
    orderBy.basePrice = sortOrder;
  } else if (sortBy === 'name') {
    orderBy.name = sortOrder;
  } else if (sortBy === 'stock') {
    orderBy.stock = sortOrder;
  } else {
    orderBy.createdAt = sortOrder;
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  try {
    // Get total count for pagination
    const total = await prisma.product.count({ where });

    // Get products
    const products = await prisma.product.findMany({
      where,
      orderBy,
      skip,
      take: pageSize,
      include: {
        categories: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            variants: true,
            images: true,
          },
        },
      },
    });

    const totalPages = Math.ceil(total / pageSize);

    return {
      products: products as ProductListItem[],
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
      },
    };
  } catch (error) {
    console.error('Error fetching products:', error);
    throw new Error('Failed to fetch products');
  }
}

export async function getProductCategories() {
  try {
    return await prisma.category.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw new Error('Failed to fetch categories');
  }
}
