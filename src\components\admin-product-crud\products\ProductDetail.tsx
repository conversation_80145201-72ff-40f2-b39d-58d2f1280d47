import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ProductStatusIndicator } from "./ProductStatusIndicator";
import { Edit, ArrowLeft, Package, Star } from "lucide-react";
import Link from "next/link";
import { ProductDetail as ProductDetailType } from "@/actions/products/get-product";

interface ProductDetailProps {
  product: ProductDetailType;
}

export function ProductDetail({ product }: ProductDetailProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK',
    }).format(price);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('sv-SE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const averageRating = product.reviews.length > 0 
    ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length 
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/products">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">{product.name}</h1>
            <p className="text-gray-600 mt-1">SKU: {product.sku}</p>
          </div>
        </div>
        <Link href={`/admin/products/${product.id}/edit`}>
          <Button>
            <Edit className="h-4 w-4 mr-2" />
            Edit Product
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle>Product Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
                  <p className="text-gray-600 mb-4">{product.description || 'No description provided'}</p>
                  
                  <div className="space-y-2">
                    <p><span className="font-medium">URL Slug:</span> /{product.slug}</p>
                    <p><span className="font-medium">SKU:</span> {product.sku}</p>
                    <p><span className="font-medium">Price:</span> {formatPrice(product.basePrice)}</p>
                    <p><span className="font-medium">Stock:</span> {product.stock}</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <ProductStatusIndicator
                    isActive={product.isActive}
                    stock={product.stock}
                    hasVariants={product.hasOption}
                  />
                  
                  {product.tags.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">Tags</h4>
                      <div className="flex flex-wrap gap-1">
                        {product.tags.map((tag) => (
                          <Badge key={tag} variant="outline">{tag}</Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Physical Properties */}
          {(product.weight || product.length || product.width || product.heigth) && (
            <Card>
              <CardHeader>
                <CardTitle>Physical Properties</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {product.weight && (
                    <div>
                      <p className="text-sm text-gray-600">Weight</p>
                      <p className="font-medium">{product.weight} kg</p>
                    </div>
                  )}
                  {product.length && (
                    <div>
                      <p className="text-sm text-gray-600">Length</p>
                      <p className="font-medium">{product.length} cm</p>
                    </div>
                  )}
                  {product.width && (
                    <div>
                      <p className="text-sm text-gray-600">Width</p>
                      <p className="font-medium">{product.width} cm</p>
                    </div>
                  )}
                  {product.heigth && (
                    <div>
                      <p className="text-sm text-gray-600">Height</p>
                      <p className="font-medium">{product.heigth} cm</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* SEO Information */}
          {(product.metaTitle || product.metaDescription) && (
            <Card>
              <CardHeader>
                <CardTitle>SEO Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {product.metaTitle && (
                  <div>
                    <p className="text-sm text-gray-600">Meta Title</p>
                    <p className="font-medium">{product.metaTitle}</p>
                  </div>
                )}
                {product.metaDescription && (
                  <div>
                    <p className="text-sm text-gray-600">Meta Description</p>
                    <p className="font-medium">{product.metaDescription}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Variants */}
          {product.variants.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Product Variants ({product.variants.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {product.variants.map((variant) => (
                    <div key={variant.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{variant.name}</p>
                        <p className="text-sm text-gray-600">SKU: {variant.sku}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatPrice(variant.basePrice)}</p>
                        <p className="text-sm text-gray-600">Stock: {variant.stock}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {product.categories.length > 0 ? (
                  product.categories.map((category) => (
                    <Badge key={category.id} variant="secondary" className="block w-fit">
                      {category.name}
                    </Badge>
                  ))
                ) : (
                  <p className="text-gray-500">No categories assigned</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Variants:</span>
                <span className="font-medium">{product._count.variants}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Images:</span>
                <span className="font-medium">{product._count.images}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Reviews:</span>
                <span className="font-medium">{product._count.reviews}</span>
              </div>
              {product.reviews.length > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Avg Rating:</span>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{averageRating.toFixed(1)}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Timestamps */}
          <Card>
            <CardHeader>
              <CardTitle>Timestamps</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm text-gray-600">Created</p>
                <p className="font-medium">{formatDate(product.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Last Updated</p>
                <p className="font-medium">{formatDate(product.updatedAt)}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
