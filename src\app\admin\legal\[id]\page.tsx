import { getLegalPageById, getLegalPageHistory } from "@/lib/actions/legal";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { LegalEditForm } from "@/components/legal/legal-edit-form";
import { LegalPageHistory } from "@/components/legal/legal-page-history";

export default async function EditLegalPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const legalPage = await getLegalPageById(id);
  const history = await getLegalPageHistory(id);

  if (!legalPage) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Edit Legal Page</h1>
        <Button asChild variant="outline">
          <Link href="/admin/legal">Back to List</Link>
        </Button>
      </div>

      <LegalEditForm legalPage={legalPage} />

      <LegalPageHistory
        pageId={legalPage.id}
        currentVersion={legalPage.version}
        history={history}
      />
    </div>
  );
}