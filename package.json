{"name": "diodtuning", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.10.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "better-auth": "^1.2.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.517.0", "marked": "^15.0.12", "next": "15.3.3", "next-cookie-consent-banner": "^1.0.6", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-form-stepper": "^2.0.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/marked": "^5.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.10.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}