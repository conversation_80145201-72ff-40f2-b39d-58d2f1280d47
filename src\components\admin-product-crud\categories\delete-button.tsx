"use client";

import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { deleteCategory } from "@/actions/categories/delete-category";


interface DeleteCategoryButtonProps {
    id:string;
    name:string;
    productCount:number;
}

export function DeleteCategoryButton({ id, name, productCount }: DeleteCategoryButtonProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const result = await deleteCategory(id);
      if (result.success) {
        router.refresh();
      } else {
        alert(result.error);
      }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch ( error) {
      alert("An error occurred while deleting the category.");
    } finally {
      setIsDeleting(false);
    }
  };

  const canDelete = productCount === 0;

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button 
          size="sm" 
          variant="outline" 
          disabled={!canDelete}
          className={!canDelete ? "opacity-50" : ""}
        >
          <Trash2 className="h-3 w-3 mr-1" />
          Delete
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Category</AlertDialogTitle>
          <AlertDialogDescription>
            {canDelete ? (
              <>
                Are you sure you want to delete the category &quot;{name}&quot;? This action cannot be undone.
              </>
            ) : (
              <>
                Cannot delete &quot;{name}&quot; because it has {productCount} product(s) assigned to it. 
                Please remove all products from this category first.
              </>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          {canDelete && (
            <AlertDialogAction 
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
