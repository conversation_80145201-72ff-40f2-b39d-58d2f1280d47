"use client";

import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";

const translations = {
  en: {
    title: "Cookies on this site",
    description: "We use cookies to improve your experience. You can choose which types to accept.",
    accept: "Accept all",
    decline: "Reject all",
    settings: "Cookie settings",
    save: "Save preferences",
    privacyLink: "Privacy Policy",
    // Cookie categories + their descriptions
    necessary: "Necessary cookies",
    analytics: "Analytics cookies",
    marketing: "Marketing cookies",
    necessaryDescription: "Required for basic website functionality",
    analyticsDescription: "Help us understand how visitors use our website",
    marketingDescription: "Used to show relevant advertisements",
  },
  sv: {
    title: "Cookies på denna webbplats",
    description: "Vi använder cookies för att förbättra din upplevelse. Du kan välja vilka typer du vill acceptera.",
    accept: "Acceptera alla",
    decline: "Avvisa alla",
    settings: "Cookie-inställningar",
    save: "Spara inställningar",
    privacyLink: "Integritetspolicy",
    // Cookie categories + descriptions (Swedish)
    necessary: "Nödvändiga cookies",
    analytics: "Analytiska cookies",
    marketing: "Marknadsföringscookies",
    necessaryDescription: "Krävs för webbplatsens grundläggande funktioner",
    analyticsDescription: "Hjälper oss förstå hur besökare använder vår webbplats",
    marketingDescription: "Används för att visa relevanta annonser",
  },
};

//Expectations
interface Props {
  lang?: "en" | "sv";  // The ? makes it optional
}

export default function CookieConsentWrapper({ lang = "sv" }: Props) {
  // useState: Remembers if the banner should be shown or hidden
  // false: Banner starts hidden, then we decide if it should show
  const [isVisible, setIsVisible] = useState(false);

  // State to show simple view or detailed settings
  const [showSettings, setShowSettings] = useState(false);

  // State to track which cookies are accepted
  const [cookiePreferences, setCookiePreferences] = useState({
    necessary: true,  // Always true - required by website, allowed to use without a consent by GDPR law
    // following cookies often use unique identifiers, therefore we are forced to ask for consent and give a user choice
    // GDPR requires explicit user consent before you can set or use non-essential cookies
    analytics: false, // User can choose
    marketing: false, // User can choose
  });

  // Get the translations for the selected language
  const t = translations[lang];

  // Placeholder functions for analytics
  const enableAnalytics = () => {
    console.log('Analytics scripts would be loaded here');
    // TODO: Add real Google Analytics initialization
  };

  const disableAnalytics = () => {
    console.log('Analytics scripts would be blocked/removed here');
    // TODO: Add real analytics blocking logic
  };

  // Placeholder functions for marketing
  const enableMarketing = () => {
    console.log('Marketing pixels would be loaded here');
    // TODO: Add Facebook Pixel, Google Ads, etc.
  };

  const disableMarketing = () => {
    console.log('Marketing pixels would be blocked/removed here');
    // TODO: Add real marketing blocking logic
  };

  // Apply consent choices -
  //  we  block or allow scripts. If user does not consent and we did not block the scripts, it would be a violation of GDPR law.


  //   const applyConsentChoices = useCallback((preferences: typeof cookiePreferences) => {
//      code logic
//   }, []);
 
//useCallback remembers your function so it doesn’t get re-created every time the component renders.

// Without useCallback, 
// applyConsentChoices would be a new function on every render, 
// so React would re-run the useEffect every time — even when it doesn't need to. 
  const applyConsentChoices = useCallback((preferences: typeof cookiePreferences) => {
    if (preferences.analytics) {
      console.log('Analytics cookies ALLOWED - tracking scripts can load');
      // load Google Analytics.
      enableAnalytics();
    } else {
      console.log('Analytics cookies BLOCKED - no tracking');
      //  block/remove analytics scripts
      disableAnalytics();
    }

    if (preferences.marketing) {
      console.log('Marketing cookies ALLOWED - advertising pixels can load');
      //  load Facebook Pixel, Google Ads, etc.
      enableMarketing();
    } else {
      console.log('Marketing cookies BLOCKED - no advertising tracking');
      //  block/remove marketing pixels
      disableMarketing();
    }
  }, []); // Empty dependency array because these functions don't change

  // Check if user has already made a choice about cookies
  useEffect(() => {
    const savedPreferences = localStorage.getItem('cookie-preferences');
    if (savedPreferences) {
      // User already made a choice - use their preferences
      const preferences = JSON.parse(savedPreferences);
      setCookiePreferences(preferences);
      applyConsentChoices(preferences); // Apply their choices
      setIsVisible(false); // Don't show banner
    } else {
      // No saved preferences - show banner
      setIsVisible(true);
    }
  }, [applyConsentChoices]);

  // Handle "Accept All" button click
  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
    };
    saveCookiePreferences(allAccepted);
  };

  // Handle "Reject All" button click
  const handleRejectAll = () => {
    const onlyNecessary = {
      necessary: true,  // Always true
      analytics: false,
      marketing: false,
    };
    saveCookiePreferences(onlyNecessary);
  };

  // Handle "Save Settings" button click
  const handleSaveSettings = () => {
    saveCookiePreferences(cookiePreferences);
  };

  // Save preferences to localStorage and hide banner
  const saveCookiePreferences = (preferences: typeof cookiePreferences) => {
    localStorage.setItem('cookie-preferences', JSON.stringify(preferences));
    setIsVisible(false);
    setShowSettings(false);

    // Apply the user's consent choices
    applyConsentChoices(preferences);
  };

  // Don't show anything if banner should be hidden
  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 max-w-md mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg text-center">{t.title}</CardTitle>
          <CardDescription className="text-center">{t.description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
        {/* Detailed Settings View (showSettings === true) */}
          {showSettings ? (
            <div className="space-y-3">
              {/* Necessary Cookies */}
              <div className="flex items-center justify-between py-2">
                <div className="flex-1">
                  <Label className="text-sm font-medium">{t.necessary}</Label>
                  <p className="text-xs text-muted-foreground">{t.necessaryDescription}</p>
                </div>
                <Switch checked={true} disabled />
              </div>

              <Separator />

              {/* Analytics Cookies */}
              <div className="flex items-center justify-between py-2">
                <div className="flex-1">
                  <Label className="text-sm font-medium">{t.analytics}</Label>
                  <p className="text-xs text-muted-foreground">{t.analyticsDescription}</p>
                </div>
                <Switch
                  checked={cookiePreferences.analytics}
                  onCheckedChange={(checked) =>
                    setCookiePreferences(prev => ({
                      ...prev,
                      analytics: checked
                    }))
                  }
                />
              </div>

              <Separator />

              {/* Marketing Cookies */}
              <div className="flex items-center justify-between py-2">
                <div className="flex-1">
                  <Label className="text-sm font-medium">{t.marketing}</Label>
                  <p className="text-xs text-muted-foreground">{t.marketingDescription}</p>
                </div>
                <Switch
                  checked={cookiePreferences.marketing}
                  onCheckedChange={(checked) =>
                    setCookiePreferences(prev => ({
                      ...prev,
                      marketing: checked
                    }))
                  }
                />
              </div>

              <div className="flex gap-2 pt-2">
                <Button onClick={() => setShowSettings(false)} variant="outline" size="sm" className="flex-1">
                  Back
                </Button>
                <Button onClick={handleSaveSettings} size="sm" className="flex-1">
                  {t.save}
                </Button>
              </div>
            </div>
          ) : (
            // Simple view if showSettings false
            <div className="space-y-2">
              <div className="flex gap-2 justify-center">
                <Button onClick={handleAcceptAll} size="sm">
                  {t.accept}
                </Button>
                <Button onClick={handleRejectAll} variant="outline" size="sm">
                  {t.decline}
                </Button>
              </div>

              <div className="flex items-center justify-center gap-4 text-xs">
                <Button
                  onClick={() => setShowSettings(true)}
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 text-xs underline"
                >
                  {t.settings}
                </Button>
                <Link
                  href="/integritetspolicy"
                  className="text-muted-foreground hover:text-foreground underline"
                >
                  {t.privacyLink}
                </Link>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
