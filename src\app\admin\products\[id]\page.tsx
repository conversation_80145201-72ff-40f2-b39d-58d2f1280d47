import { getProduct } from "@/actions/products/get-product";
import { notFound } from "next/navigation";
import { ProductDetail } from "@/components/admin-product-crud/products/ProductDetail";

interface ProductDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function ProductDetailPage({ params }: ProductDetailPageProps) {
  const { id } = await params;
  
  try {
    const product = await getProduct(id);

    if (!product) {
      notFound();
    }

    return (
      <div className="container mx-auto py-6">
        <ProductDetail product={product} />
      </div>
    );
  } catch (error) {
    console.error('Error loading product:', error);
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error Loading Product
          </h2>
          <p className="text-gray-600 mb-4">
            There was an error loading the product details. Please try again.
          </p>
          <button 
            onClick={() => window.location.reload()} 
            className="text-blue-600 hover:text-blue-800"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }
}
