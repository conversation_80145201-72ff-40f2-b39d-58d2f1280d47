"use server";

import { prisma } from "@/lib/database";
import { redirect } from "next/navigation";
import { z } from "zod";

// Simplified validation schema
const productV2Schema = z.object({
  // Required fields
  name: z.string().min(1, "Product name is required"),
  slug: z.string().min(1, "URL slug is required"),
  basePrice: z.coerce.number().min(0, "Price must be 0 or greater"),
  sku: z.string().min(1, "SKU is required"),
  stock: z.coerce.number().int().min(0, "Stock must be 0 or greater"),
  categoryIds: z.array(z.string()).min(1, "At least one category is required"),
  
  // Optional fields
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  hasOption: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
  
  // Shipping dimensions (optional)
  weight: z.coerce.number().optional(),
  length: z.coerce.number().optional(),
  width: z.coerce.number().optional(),
  heigth: z.coerce.number().optional(),
  
  // SEO fields (optional)
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
});

export type ActionState = {
  message: string;
  error: string;
  fieldErrors: Record<string, string>;
};

export async function createProductV2(
  prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  try {
    // Extract form data
    const rawData = Object.fromEntries(formData.entries());
    
    // Process the data
    const processedData = {
      ...rawData,
      // Parse JSON fields
      categoryIds: rawData.categoryIds ? JSON.parse(rawData.categoryIds as string) : [],
      tags: rawData.tags ? JSON.parse(rawData.tags as string) : [],
      // Convert checkboxes to booleans
      isActive: formData.has('isActive'),
      hasOption: formData.has('hasOption'),
    };

    // Validate the data
    const result = productV2Schema.safeParse(processedData);
    
    if (!result.success) {
      const fieldErrors = result.error.flatten().fieldErrors;
      const friendlyErrors: Record<string, string> = {};
      
      Object.entries(fieldErrors).forEach(([field, errors]) => {
        if (errors && errors.length > 0) {
          friendlyErrors[field] = errors[0];
        }
      });

      return {
        message: "",
        error: "Please fix the errors below and try again.",
        fieldErrors: friendlyErrors,
      };
    }

    const data = result.data;

    // Check for unique constraints before creating
    const existingSlug = await prisma.product.findUnique({
      where: { slug: data.slug },
    });

    if (existingSlug) {
      return {
        message: "",
        error: "A product with this URL slug already exists.",
        fieldErrors: { slug: "This slug is already taken" },
      };
    }

    const existingSku = await prisma.product.findUnique({
      where: { sku: data.sku },
    });

    if (existingSku) {
      return {
        message: "",
        error: "A product with this SKU already exists.",
        fieldErrors: { sku: "This SKU is already taken" },
      };
    }

    // Create the product
    await prisma.product.create({
      data: {
        name: data.name,
        slug: data.slug,
        description: data.description || null,
        basePrice: data.basePrice,
        sku: data.sku,
        stock: data.stock,
        isActive: data.isActive,
        hasOption: data.hasOption,
        weight: data.weight || null,
        length: data.length || null,
        width: data.width || null,
        heigth: data.heigth || null,
        metaTitle: data.metaTitle || null,
        metaDescription: data.metaDescription || null,
        tags: data.tags || [],
        categories: {
          connect: data.categoryIds.map((id: string) => ({ id })),
        },
      },
    });

    // Success - redirect to products list
    redirect("/admin/products");
    
  } catch (error) {
    console.error("Error creating product:", error);
    
    // Handle database constraint errors
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint failed on the fields: (`slug`)')) {
        return {
          message: "",
          error: "A product with this URL slug already exists.",
          fieldErrors: { slug: "This slug is already taken" },
        };
      }

      if (error.message.includes('Unique constraint failed on the fields: (`sku`)')) {
        return {
          message: "",
          error: "A product with this SKU already exists.",
          fieldErrors: { sku: "This SKU is already taken" },
        };
      }
    }

    return {
      message: "",
      error: "An unexpected error occurred while creating the product. Please try again.",
      fieldErrors: {},
    };
  }
}
