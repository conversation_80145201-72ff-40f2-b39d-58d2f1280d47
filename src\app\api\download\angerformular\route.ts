import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Generate HTML that Word can import with proper UTF-8 encoding
    const wordContent = generateWordDocument();
    return new NextResponse(wordContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Content-Disposition': 'attachment; filename="angerformular.html"',
      },
    });
  } catch (error) {
    console.error('Error serving download:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function generateWordDocument(): string {
  // Generate HTML that Word can import properly with UTF-8 encoding
  const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Ångerformulär</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        h1 { text-align: center; font-size: 24px; margin-bottom: 30px; }
        .company { text-align: center; margin-bottom: 30px; font-weight: bold; }
        .field { margin-bottom: 20px; }
        .field label { font-weight: bold; display: block; margin-bottom: 5px; }
        .line { border-bottom: 1px solid #000; display: inline-block; width: 300px; margin-left: 10px; }
        .signature { margin-top: 40px; }
    </style>
</head>
<body>
    <h1>ÅNGERFORMULÄR</h1>

    <div class="company">
        Luxe Group AB (Diodtuning.se)<br>
        E-post: <EMAIL> | Telefon: 08-501 183 99
    </div>

    <p>Jag meddelar härmed att jag ångrar mitt köp enligt distansavtalslagen.</p>

    <div class="field">
        <label>Ordernummer:</label>
        <span class="line"></span>
    </div>

    <div class="field">
        <label>Beställningsdatum:</label>
        <span class="line"></span>
    </div>

    <div class="field">
        <label>Produkter som ångras:</label><br>
        <span class="line" style="width: 400px;"></span><br><br>
        <span class="line" style="width: 400px;"></span>
    </div>

    <h3>Kunduppgifter:</h3>

    <div class="field">
        <label>Namn:</label>
        <span class="line"></span>
    </div>

    <div class="field">
        <label>Adress:</label>
        <span class="line"></span>
    </div>

    <div class="field">
        <label>Postnummer och ort:</label>
        <span class="line"></span>
    </div>

    <div class="field">
        <label>Telefon:</label>
        <span class="line"></span>
    </div>

    <div class="field">
        <label>E-post:</label>
        <span class="line"></span>
    </div>

    <div class="field">
        <label>Datum:</label>
        <span class="line"></span>
    </div>

    <div class="signature">
        <label>Underskrift:</label>
        <span class="line" style="width: 250px;"></span>
    </div>

    <p style="margin-top: 40px; font-size: 12px; text-align: center;">
        Skicka det ifyllda formuläret till: <EMAIL>
    </p>
</body>
</html>`;

  return htmlContent;
}
