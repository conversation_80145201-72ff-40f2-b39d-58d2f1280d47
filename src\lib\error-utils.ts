import { ZodError } from "zod";

export type ActionResult<T = unknown> = {
  success: boolean;
  data?: T;
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export function createSuccessResult<T>(data: T, message?: string): ActionResult<T> {
  return {
    success: true,
    data,
    error: message,
  };
}

export function createErrorResult(error: string, fieldErrors?: Record<string, string[]>): ActionResult {
  return {
    success: false,
    error,
    fieldErrors,
  };
}

export function handleActionError(error: unknown): ActionResult {
  console.error("Action error:", error);
  
  if (error instanceof ZodError) {
    const fieldErrors: Record<string, string[]> = {};
    
    error.errors.forEach((err) => {
      const path = err.path.join(".");
      if (!fieldErrors[path]) {
        fieldErrors[path] = [];
      }
      fieldErrors[path].push(getUserFriendlyErrorMessage(err.message, path));
    });
    
    return createErrorResult("Please check the form for errors", fieldErrors);
  }
  
  if (error instanceof Error) {
    return createErrorResult(getUserFriendlyErrorMessage(error.message));
  }
  
  return createErrorResult("An unexpected error occurred. Please try again.");
}

function getUserFriendlyErrorMessage(message: string, field?: string): string {
  // Convert technical Zod messages to user-friendly ones
  const friendlyMessages: Record<string, string> = {
    "String must contain at least 3 character(s)": `${field ? capitalizeFirst(field) : "This field"} must be at least 3 characters long`,
    "String must contain at least 10 character(s)": `${field ? capitalizeFirst(field) : "This field"} must be at least 10 characters long`,
    "Invalid enum value": `Please select a valid option${field ? ` for ${field}` : ""}`,
    "Required": `${field ? capitalizeFirst(field) : "This field"} is required`,
    "Expected string, received undefined": `${field ? capitalizeFirst(field) : "This field"} is required`,
  };
  
  // Check for regex validation errors
  if (message.includes("Invalid")) {
    if (field === "slug") {
      return "Slug can only contain lowercase letters, numbers, and hyphens";
    }
    if (field === "version") {
      return "Version must be in format X.Y (e.g., 1.0, 2.1)";
    }
  }
  
  return friendlyMessages[message] || message;
}

function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
