"use client";

import { useState, useActionState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { X, ArrowLeft } from "lucide-react";
import { updateProduct } from "@/actions/products/update-product";
import Link from "next/link";

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface Product {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  basePrice: number;
  sku: string;
  stock: number;
  isActive: boolean;
  hasOption: boolean;
  weight: number | null;
  length: number | null;
  width: number | null;
  heigth: number | null;
  metaTitle: string | null;
  metaDescription: string | null;
  tags: string[];
  categories: Category[];
}

interface EditProductFormProps {
  product: Product;
  categories: Category[];
}

export default function EditProductForm({ product, categories }: EditProductFormProps) {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(updateProduct, {
    message: "",
    error: "",
    fieldErrors: {},
  });

  // Initialize state with existing product data
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    product.categories.map(cat => cat.id)
  );
  const [tags, setTags] = useState<string[]>(product.tags || []);
  const [tagInput, setTagInput] = useState("");

  // Tag management
  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleTagKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  // Category management
  const toggleCategory = (categoryId: string) => {
    setSelectedCategories(prev => 
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <form action={formAction} className="space-y-4">
        {/* Hidden fields */}
        <input type="hidden" name="id" value={product.id} />
        <input type="hidden" name="categoryIds" value={JSON.stringify(selectedCategories)} />
        <input type="hidden" name="tags" value={JSON.stringify(tags)} />

        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link href={`/admin/products/${product.id}`}>
            <Button type="button" variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Product
            </Button>
          </Link>
          <h2 className="text-2xl font-bold">Edit Product</h2>
        </div>

        {/* Error Display */}
        {state.error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-3">
            <p className="text-red-800 text-sm">{state.error}</p>
            {state.fieldErrors && Object.keys(state.fieldErrors).length > 0 && (
              <ul className="mt-1 list-disc list-inside text-xs text-red-700">
                {Object.entries(state.fieldErrors).map(([field, error]) => (
                  <li key={field}>{field}: {error}</li>
                ))}
              </ul>
            )}
          </div>
        )}

        {/* Essential Information */}
        <div className="bg-white border rounded-lg p-4 space-y-3">
          <h3 className="font-semibold text-lg">Essential Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <Label htmlFor="name" className="text-sm font-medium">Product Name *</Label>
              <Input
                id="name"
                name="name"
                defaultValue={product.name}
                placeholder="Enter product name"
                className={`mt-1 ${state.fieldErrors?.name ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.name && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="slug" className="text-sm font-medium">URL Slug *</Label>
              <Input
                id="slug"
                name="slug"
                defaultValue={product.slug}
                placeholder="product-url-slug"
                className={`mt-1 ${state.fieldErrors?.slug ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.slug && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.slug}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="description" className="text-sm font-medium">Description</Label>
            <Textarea
              id="description"
              name="description"
              defaultValue={product.description || ""}
              placeholder="Product description"
              rows={2}
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <Label htmlFor="basePrice" className="text-sm font-medium">Price (SEK) *</Label>
              <Input
                id="basePrice"
                name="basePrice"
                type="number"
                step="0.01"
                defaultValue={product.basePrice}
                placeholder="0.00"
                className={`mt-1 ${state.fieldErrors?.basePrice ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.basePrice && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.basePrice}</p>
              )}
            </div>

            <div>
              <Label htmlFor="sku" className="text-sm font-medium">SKU *</Label>
              <Input
                id="sku"
                name="sku"
                defaultValue={product.sku}
                placeholder="PRODUCT-SKU-001"
                className={`mt-1 ${state.fieldErrors?.sku ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.sku && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.sku}</p>
              )}
            </div>

            <div>
              <Label htmlFor="stock" className="text-sm font-medium">Stock *</Label>
              <Input
                id="stock"
                name="stock"
                type="number"
                defaultValue={product.stock}
                placeholder="0"
                className={`mt-1 ${state.fieldErrors?.stock ? "border-red-500" : ""}`}
              />
              {state.fieldErrors?.stock && (
                <p className="text-xs text-red-600 mt-1">{state.fieldErrors.stock}</p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Checkbox id="isActive" name="isActive" defaultChecked={product.isActive} />
              <Label htmlFor="isActive" className="text-sm">Active</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="hasOption" name="hasOption" defaultChecked={product.hasOption} />
              <Label htmlFor="hasOption" className="text-sm">Has variants</Label>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="bg-white border rounded-lg p-4 space-y-3">
          <h3 className="font-semibold">Categories *</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 max-h-32 overflow-y-auto">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category.id}`}
                  checked={selectedCategories.includes(category.id)}
                  onCheckedChange={() => toggleCategory(category.id)}
                />
                <Label htmlFor={`category-${category.id}`} className="text-sm">
                  {category.name}
                </Label>
              </div>
            ))}
          </div>
          {state.fieldErrors?.categoryIds && (
            <p className="text-xs text-red-600">{state.fieldErrors.categoryIds}</p>
          )}
        </div>

        {/* Optional Fields */}
        <details className="bg-gray-50 border rounded-lg" defaultOpen>
          <summary className="p-4 cursor-pointer font-semibold">Optional Fields</summary>
          <div className="p-4 pt-0 space-y-4">
            
            {/* Physical Properties */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Physical Properties</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <div>
                  <Label htmlFor="weight" className="text-xs">Weight (kg)</Label>
                  <Input
                    id="weight"
                    name="weight"
                    type="number"
                    step="0.001"
                    defaultValue={product.weight || ""}
                    placeholder="0.000"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="length" className="text-xs">Length (cm)</Label>
                  <Input
                    id="length"
                    name="length"
                    type="number"
                    step="0.01"
                    defaultValue={product.length || ""}
                    placeholder="0.00"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="width" className="text-xs">Width (cm)</Label>
                  <Input
                    id="width"
                    name="width"
                    type="number"
                    step="0.01"
                    defaultValue={product.width || ""}
                    placeholder="0.00"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="heigth" className="text-xs">Height (cm)</Label>
                  <Input
                    id="heigth"
                    name="heigth"
                    type="number"
                    step="0.01"
                    defaultValue={product.heigth || ""}
                    placeholder="0.00"
                    className="mt-1"
                  />
                </div>
              </div>
            </div>

            {/* SEO Fields */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">SEO Fields</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="metaTitle" className="text-xs">Meta Title</Label>
                  <Input
                    id="metaTitle"
                    name="metaTitle"
                    defaultValue={product.metaTitle || ""}
                    placeholder="SEO title"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="metaDescription" className="text-xs">Meta Description</Label>
                  <Input
                    id="metaDescription"
                    name="metaDescription"
                    defaultValue={product.metaDescription || ""}
                    placeholder="SEO description"
                    className="mt-1"
                  />
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Tags</h4>
              <div className="flex space-x-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={handleTagKeyDown}
                  placeholder="Add a tag"
                  className="flex-1"
                />
                <Button type="button" onClick={addTag} variant="outline" size="sm">
                  Add
                </Button>
              </div>
              
              <div className="flex flex-wrap gap-1">
                {tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs flex items-center gap-1">
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </details>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push(`/admin/products/${product.id}`)}
          >
            Cancel
          </Button>

          <Button type="submit" disabled={isPending}>
            {isPending ? "Updating..." : "Update Product"}
          </Button>
        </div>
      </form>
    </div>
  );
}
